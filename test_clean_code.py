#!/usr/bin/env python
"""
Test script for the clean financial journal testing code.
This script tests the main functionality without requiring user input.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def create_sample_data():
    """Create sample financial data for testing."""
    print("Creating sample financial data...")
    
    # Generate sample data
    np.random.seed(42)
    n_records = 100
    
    # Create date range
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    date_range = pd.date_range(start_date, end_date, freq='D')
    
    data = {
        'Account_Code': np.random.choice(['1000', '2000', '3000', '4000', '5000'], n_records),
        'Document_No': range(1, n_records + 1),
        'Date': np.random.choice(date_range, n_records),
        'Amount': np.random.uniform(-10000, 10000, n_records),
        'Description': [f'Transaction {i}' for i in range(n_records)],
        'Time': [datetime.now().time() for _ in range(n_records)]
    }
    
    # Add some round numbers for testing
    round_indices = np.random.choice(n_records, 10, replace=False)
    for i in round_indices[:5]:
        data['Amount'][i] = np.random.choice([1000, 2000, 5000, 10000])  # 000 endings
    for i in round_indices[5:]:
        data['Amount'][i] = np.random.choice([999, 1999, 4999, 9999])   # 999 endings
    
    df = pd.DataFrame(data)
    
    # Add some reversal entries
    reversal_indices = np.random.choice(n_records, 5, replace=False)
    for i in reversal_indices:
        df.loc[i, 'Description'] = f'Reversal entry {i}'
    
    print(f"✓ Created sample data with {len(df)} records")
    return df

def test_imports():
    """Test if all required modules can be imported."""
    try:
        from code_clean import (
            initialize_analysis_environment,
            optimize_dataframe_memory,
            load_data_from_file,
            configure_analysis_parameters
        )
        print("✓ Successfully imported functions from code_clean.py")
        return True
    except ImportError as e:
        print(f"✗ Error importing from code_clean.py: {e}")
        return False

def test_basic_functionality():
    """Test basic functionality without Excel operations."""
    print("\nTesting basic functionality...")
    
    if not test_imports():
        return False
    
    from code_clean import initialize_analysis_environment, optimize_dataframe_memory
    
    # Test environment initialization
    try:
        initialize_analysis_environment()
        print("✓ Environment initialization test passed")
    except Exception as e:
        print(f"✗ Environment initialization failed: {e}")
        return False
    
    # Test memory optimization
    try:
        df = pd.DataFrame({
            'int_col': [1, 2, 3, 4, 5],
            'float_col': [1.1, 2.2, 3.3, 4.4, 5.5],
            'str_col': ['A', 'B', 'A', 'B', 'A']
        })
        
        original_memory = df.memory_usage(deep=True).sum()
        optimized_df = optimize_dataframe_memory(df.copy())
        optimized_memory = optimized_df.memory_usage(deep=True).sum()
        
        print(f"✓ Memory optimization test passed")
        print(f"  Original memory: {original_memory} bytes")
        print(f"  Optimized memory: {optimized_memory} bytes")
    except Exception as e:
        print(f"✗ Memory optimization failed: {e}")
        return False
    
    return True

def main():
    """Main test function."""
    print("="*60)
    print("CLEAN CODE VALIDATION")
    print("="*60)
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n✗ Basic functionality tests failed")
        return
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save sample data for manual testing
    sample_file = "sample_financial_data.xlsx"
    try:
        sample_data.to_excel(sample_file, index=False)
        print(f"✓ Sample data saved to: {sample_file}")
    except Exception as e:
        print(f"✗ Error saving sample data: {e}")
    
    print("\n" + "="*60)
    print("CLEAN CODE VALIDATION COMPLETED")
    print("="*60)
    print("✓ All basic tests passed!")
    print(f"✓ Sample data created: {sample_file}")
    print("\nTo run the full analysis:")
    print("1. Run: python code_clean.py")
    print(f"2. Use the sample file: {sample_file}")
    print("3. Follow the interactive prompts")
    print("\nExample configuration:")
    print("- Client name: Test Company")
    print("- Analysis period: 2023 Q4")
    print("- Account codes column: Account_Code")
    print("- Document number column: Document_No")
    print("- Date column: Date")
    print("- Amount column: Amount")
    print("- Description column: Description")
    print("- Time column: na")
    print("- Date format: %Y-%m-%d")
    print("- Time format: na")

if __name__ == "__main__":
    main()
