#!/usr/bin/env python
# coding: utf-8

"""
Financial Journal Testing Script - OPTIMIZED VERSION
===================================================
This script performs comprehensive analysis on financial journal entries including:
- Round number detection (000, 999 endings)
- Holiday and weekend posting analysis
- Odd hours transaction detection
- Monthly transaction summaries
- Reversal entry identification
- Gap analysis in journal numbers
- Revenue code debit analysis
- Account matching tests

Optimized for terminal-only execution without GUI dependencies.
"""

import warnings
warnings.filterwarnings('ignore')

# Core data processing libraries
import pandas as pd
import xlwings as xw
import numpy as np
import re
import os
import datetime
import calendar

# Configure pandas display format for better readability
pd.options.display.float_format = '{:,.2f}'.format

# ============================================================================
# GLOBAL VARIABLES
# ============================================================================

# Global variables for configuration and data storage
wb = None  # Excel workbook object
sheet_name = ""  # Current sheet name
letter = []  # Excel column letters for formatting
n = 0  # Counter variable
num = 1  # Tab counter for Excel sheets

# Account classification lists
rev_code = []  # Revenue account codes
bank_acc = []  # Bank account codes  
pre_acc = []  # Prepayment account codes
accrual_acc = []  # Accrual account codes
pl_acc = []  # P&L account codes

# Column name mappings from dataset
account_codes = ""  # Account codes column name
doc_no = ""  # Document number column name
date = ""  # Date column name
amount = ""  # Amount column name
acc_description = ""  # Account description column name
acc_type = ""  # Account type column name
time = ""  # Time column name
post_by = ""  # Posted by column name
date_format = ""  # Date format string
time_format = ""  # Time format string

# Analysis parameters
client_name = ""  # Client name for reports
client_period = ""  # Analysis period
holiday_dates = []  # List of holiday dates
link = []  # Links to Excel tabs

# Data storage
df = None  # Working dataframe
dataset = None  # Main dataset
monthly_tab = ""
value = ""

# ============================================================================
# HELPER FUNCTIONS
# ============================================================================

def initialize_analysis_environment():
    """Initialize the analysis environment with default settings."""
    global letter, num, link
    
    # Initialize Excel column letters for formatting
    letter = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    letter.extend([f"A{chr(i)}" for i in range(ord('A'), ord('Z') + 1)])
    
    # Initialize counters
    num = 1
    link = []
    
    print("Analysis environment initialized successfully")


def _setup_worksheet_header(worksheet, title):
    """Helper function to set up standard worksheet headers."""
    worksheet["A1"].value = client_name
    worksheet.range('A1').api.Font.Bold = True
    worksheet["A2"].value = client_period
    worksheet.range('A2').api.Font.Bold = True
    worksheet["A3"].value = title
    worksheet.range('A3').api.Font.Bold = True


def _apply_data_formatting(worksheet, data_shape, header_shape, data, include_borders=True):
    """Helper function to apply consistent formatting to data ranges."""
    # Convert datetime objects to strings to avoid Excel issues
    if not data.empty:
        data = data.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
    
    # Apply formatting
    if include_borders and header_shape:
        worksheet.range(data_shape).api.Borders(3).LineStyle = 1
        worksheet.range(data_shape).api.Borders(2).LineStyle = 1
        worksheet.range(header_shape).api.Borders(3).LineStyle = 1
        worksheet.range(header_shape).api.Borders(2).LineStyle = 1
        worksheet.range(header_shape).font.bold = True
        worksheet.range(header_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    
    # Set font and auto-fit
    worksheet.range(data_shape).font.name = 'Times New Roman'
    worksheet.range(data_shape).columns.autofit()
    
    return data


def optimize_dataframe_memory(df):
    """Optimize DataFrame memory usage by downcasting numeric types."""
    if df.empty:
        return df
        
    # Optimize numeric columns
    for col in df.select_dtypes(include=['int64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='integer')
    
    for col in df.select_dtypes(include=['float64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')
    
    # Optimize object columns that might be categorical
    for col in df.select_dtypes(include=['object']).columns:
        if df[col].nunique() / len(df) < 0.5:  # If less than 50% unique values
            df[col] = df[col].astype('category')
    
    return df


def load_data_from_file(file_path):
    """Load data from Excel or CSV file with error handling."""
    try:
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            data = pd.read_excel(file_path)
        elif file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:
            print("Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv)")
            return None
            
        print(f"Data loaded successfully: {len(data)} rows, {len(data.columns)} columns")
        return optimize_dataframe_memory(data)
        
    except Exception as e:
        print(f"Error loading data: {str(e)}")
        return None


# ============================================================================
# SUMMARY SHEET FUNCTION
# ============================================================================

def summary_sheet():
    """Creates a summary sheet in Excel workbook with test descriptions."""
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset
    
    # Get active sheet and set it up as summary
    summary_sheet = wb.sheets.active
    sheet_name = "Summary"
    summary_sheet.name = sheet_name

    # Apply borders to the test summary table
    summary_sheet.range("A5:B21").api.Borders(3).LineStyle = 1
    summary_sheet.range("A5:B20").api.Borders(2).LineStyle = 1

    # Set header information
    summary_sheet["A1"].value = client_name
    summary_sheet.range('A1').api.Font.Bold = True
    
    summary_sheet["A2"].value = client_period
    summary_sheet.range('A2').api.Font.Bold = True
    summary_sheet["A3"].value = "Subject Journal Testing"
    summary_sheet.range('A3').api.Font.Bold = True

    # Create table headers with formatting
    summary_sheet["A5"].value = "Test"
    summary_sheet.range('A5').api.Font.Bold = True
    summary_sheet['A5'].color = 255, 200, 255
    summary_sheet['A5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    summary_sheet["B5"].value = "Potential exceptions"
    summary_sheet.range('B5').api.Font.Bold = True
    summary_sheet['B5'].color = 255, 200, 255
    summary_sheet['B5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

    # List all available tests
    test_descriptions = [
        "Round entries ,000 or ,999",
        "Date of postings: weekends, bank holidays etc.",
        "Timings of postings - any postings on odd hours",
        "Total amount of transactions per month",
        "Reversed Journal Entries",
        "Gaps/jumps in Journal Entry numbers",
        "Summary of Debit transactions in Revenue codes",
        "Prepayments vs Bank",
        "Accruals vs Bank",
        "Bank accounts vs PnL accounts.",
        "Postings by directors on Companies house",
        "Possible duplicate Journal entries",
        "Fraud Word Check",
        "Sales Chronological Testing",
        "Credits in Revenue"
    ]
    
    # Populate test descriptions
    for i, description in enumerate(test_descriptions, start=6):
        summary_sheet[f"A{i}"].value = description
    
    # Add notes about limitations
    notes = [
        "Note 1: No any references were provided regarding details of Time posted",
        f"Note 2: Impossible to perform Gap test as {doc_no} contains characters like text, slashes and hyphens.",
        "Note 3: No any references were provided regarding details of users ID and employee key account."
    ]
    
    for i, note in enumerate(notes, start=23):
        summary_sheet[f"A{i}"].value = note
        summary_sheet.range(f'A{i}').api.Font.Bold = True
    
    # Set font for entire summary
    summary_sheet.range("A1:A25").font.name = 'Times New Roman'
    
    # Auto-fit columns
    wb.sheets[summary_sheet].autofit('c')
    
    print("Summary sheet created successfully")


# ============================================================================
# TEST 1: ROUND ENTRIES ANALYSIS
# ============================================================================

def round_entries(Amount):
    """
    Analyzes journal entries for round numbers ending in 000 or 999.
    These patterns may indicate potential manipulation or unusual transactions.
    """
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset

    link.append("-")  # Initialize link placeholder

    try:
        if amount != "na":
            print(f"Analyzing round entries in column: {amount}")

            # Filter out null values and clean amount data
            entries = dataset[dataset[amount].notnull()].copy()
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",", "")
            entries = entries[entries[amount] != ""]

            # Find entries ending in 000 (using optimized regex)
            entries_000 = entries[
                entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$", na=False)
            ].copy()
            if not entries_000.empty:
                entries_000[amount] = pd.to_numeric(entries_000[amount], errors='coerce')
                entries_000 = entries_000.dropna(subset=[amount]).sort_values(amount, ascending=False)

            # Find entries ending in 999 (using optimized regex)
            entries_999 = entries[
                entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$", na=False)
            ].copy()
            if not entries_999.empty:
                entries_999[amount] = pd.to_numeric(entries_999[amount], errors='coerce')
                entries_999 = entries_999.dropna(subset=[amount]).sort_values(amount, ascending=False)

            # Combine results
            round_entries = pd.concat([entries_000, entries_999], ignore_index=True)

            print(f"Found {len(entries_000)} entries ending in 000")
            print(f"Found {len(entries_999)} entries ending in 999")

            # Create Excel worksheet for round entries analysis
            round_entries_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            link[0] = f"Tab {num}"
            num += 1

            # Set up worksheet headers and formatting
            _setup_worksheet_header(round_entries_tab, "Round entries ,000 or ,999")

            # Add analysis description
            round_entries_tab["A5"].value = "Objective: To find out unusual round number entries in journals."
            round_entries_tab.range('A5').api.Font.Bold = True

            round_entries_tab["A7"].value = 'Method: Filtered all the entries ending with "000" and "999".'
            round_entries_tab.range('A7').api.Font.Bold = True

            # Set up section headers with color coding
            round_entries_tab['A9'].color = 255, 200, 255
            round_entries_tab["B9"].value = "Amount ending in '000'"
            round_entries_tab['A10'].color = 221, 235, 247
            round_entries_tab["B10"].value = "Amount ending in '999'"

            # Calculate dimensions for Excel output
            if not entries_000.empty:
                r = entries_000.shape[0] + 12
                c = entries_000.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                # Apply color coding and formatting for 000 entries
                round_entries_tab.range(data_shape).color = 255, 200, 255
                entries_000 = _apply_data_formatting(round_entries_tab, data_shape, c_shape, entries_000)
                round_entries_tab["A12"].options(pd.DataFrame, index=False).value = entries_000

            # Handle 999 entries section
            if not entries_999.empty:
                r = (entries_000.shape[0] if not entries_000.empty else 0) + 13
                c = entries_999.shape[1]

                data_shape = f"A{r}:{letter[c]}{r + entries_999.shape[0]}"

                # Apply color coding and formatting for 999 entries
                round_entries_tab.range(data_shape).color = 221, 235, 247
                entries_999 = _apply_data_formatting(round_entries_tab, data_shape, "", entries_999, include_borders=True)
                round_entries_tab[f"A{r}"].options(pd.DataFrame, index=False, header=None).value = entries_999

            # Set overall formatting
            total_rows = (entries_000.shape[0] if not entries_000.empty else 0) + (entries_999.shape[0] if not entries_999.empty else 0) + 12
            max_cols = max(entries_000.shape[1] if not entries_000.empty else 0,
                          entries_999.shape[1] if not entries_999.empty else 0)

            if max_cols > 0:
                round_entries_tab.range(f"A1:{letter[max_cols]}{total_rows}").font.name = 'Times New Roman'
                round_entries_tab.range(f"A12:{letter[max_cols]}{total_rows}").columns.autofit()

            print(f"Round entries analysis completed. Total entries found: {len(round_entries)}")

            # Add conclusion based on findings
            cell_no = (entries_000.shape[0] if not entries_000.empty else 0) + \
                     (entries_999.shape[0] if not entries_999.empty else 0) + 16

            # Determine conclusion message
            has_000 = not entries_000.empty and len(entries_000) > 0
            has_999 = not entries_999.empty and len(entries_999) > 0

            if has_000 and has_999:
                conclusion = "Conclusion: Entries ending with '000' & '999' found."
            elif has_000:
                conclusion = "Conclusion: Entries ending with '000' found."
            elif has_999:
                conclusion = "Conclusion: Entries ending with '999' found."
            else:
                conclusion = "Conclusion: No Entries ending with '000' & '999' found."

            round_entries_tab[f"A{cell_no}"].value = conclusion
            round_entries_tab.range(f'A{cell_no}').api.Font.Bold = True
            round_entries_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

            print(conclusion)

        else:
            round_entries = 'Col Name Not Given'
            print("Error: Amount column name not provided")

        return round_entries

    except Exception as e:
        error_msg = f"Error in round_entries analysis: {str(e)}"
        print(error_msg)
        return error_msg


# ============================================================================
# CONFIGURATION AND MAIN FUNCTIONS
# ============================================================================

def configure_analysis_parameters():
    """Interactive configuration of analysis parameters via terminal input."""
    print("\n" + "="*60)
    print("FINANCIAL JOURNAL TESTING - CONFIGURATION")
    print("="*60)

    config = {}

    # Basic information
    config['client_name'] = input("Enter client name: ").strip()
    config['client_period'] = input("Enter analysis period (e.g., 2023 Q1): ").strip()

    # Column mappings
    print("\nColumn Mapping Configuration:")
    config['account_codes'] = input("Enter account codes column name: ").strip()
    config['doc_no'] = input("Enter document number column name: ").strip()
    config['date'] = input("Enter date column name: ").strip()
    config['amount'] = input("Enter amount column name: ").strip()
    config['acc_description'] = input("Enter account description column name: ").strip()
    config['time'] = input("Enter time column name (or 'na' if not available): ").strip()

    # Date/time formats
    config['date_format'] = input("Enter date format (e.g., %Y-%m-%d): ").strip()
    config['time_format'] = input("Enter time format (e.g., %H:%M:%S) or 'na': ").strip()

    # Account classifications
    print("\nAccount Classification (comma-separated codes):")
    config['rev_code'] = [x.strip() for x in input("Enter revenue account codes: ").split(',') if x.strip()]
    config['bank_acc'] = [x.strip() for x in input("Enter bank account codes: ").split(',') if x.strip()]
    config['pre_acc'] = [x.strip() for x in input("Enter prepayment account codes: ").split(',') if x.strip()]
    config['accrual_acc'] = [x.strip() for x in input("Enter accrual account codes: ").split(',') if x.strip()]
    config['pl_acc'] = [x.strip() for x in input("Enter P&L account codes: ").split(',') if x.strip()]

    # Holiday dates
    holiday_input = input("Enter holiday dates (YYYY-MM-DD, comma-separated) or 'na': ").strip()
    if holiday_input.lower() != 'na':
        try:
            config['holiday_dates'] = [pd.to_datetime(x.strip()) for x in holiday_input.split(',') if x.strip()]
        except:
            config['holiday_dates'] = []
            print("Warning: Invalid holiday date format, using empty list")
    else:
        config['holiday_dates'] = []

    return config


def run_basic_analysis(data, config, output_file):
    """Run basic analysis tests on the dataset."""
    global wb, dataset, client_name, client_period, holiday_dates
    global account_codes, doc_no, date, amount, acc_description, time
    global date_format, time_format, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc

    # Set global variables
    dataset = data
    client_name = config['client_name']
    client_period = config['client_period']
    holiday_dates = config['holiday_dates']
    account_codes = config['account_codes']
    doc_no = config['doc_no']
    date = config['date']
    amount = config['amount']
    acc_description = config['acc_description']
    time = config['time']
    date_format = config['date_format']
    time_format = config['time_format']
    rev_code = config['rev_code']
    bank_acc = config['bank_acc']
    pre_acc = config['pre_acc']
    accrual_acc = config['accrual_acc']
    pl_acc = config['pl_acc']

    # Initialize Excel workbook with proper naming
    try:
        wb = xw.Book()
        # Ensure the output file has the correct format
        if not output_file.endswith('.xlsx'):
            output_file += '.xlsx'

        # Extract client name for proper formatting if needed
        if " - Journal Testing" not in output_file:
            output_file = f"{client_name} - Journal Testing.xlsx"

        wb.save(output_file)
        print(f"Excel workbook created: {output_file}")
    except Exception as e:
        print(f"Error creating Excel workbook: {e}")
        return None

    results = {}

    print("\n" + "="*60)
    print("RUNNING BASIC ANALYSIS")
    print("="*60)

    # Test 1: Round entries
    print("\n1. Analyzing round entries (000, 999)...")
    try:
        results['round_entries'] = round_entries(amount)
        print("✓ Round entries analysis completed")
    except Exception as e:
        print(f"✗ Round entries analysis failed: {e}")
        results['round_entries'] = f"Error: {e}"

    # Create summary sheet
    print("\n2. Creating summary sheet...")
    try:
        summary_sheet()
        print("✓ Summary sheet created")
    except Exception as e:
        print(f"✗ Summary sheet creation failed: {e}")

    # Save workbook with final naming
    try:
        final_filename = f"{client_name} - Journal Testing.xlsx"
        wb.save(final_filename)

        print(f"\n✓ Analysis completed successfully!")
        print(f"Results saved to: {final_filename}")

    except Exception as e:
        print(f"✗ Error saving workbook: {e}")

    return results


def main():
    """Main function to run the financial journal testing application."""
    print("="*80)
    print("FINANCIAL JOURNAL TESTING SYSTEM")
    print("Optimized Terminal Version")
    print("="*80)

    # Initialize environment
    initialize_analysis_environment()

    # Get data file path
    file_path = input("\nEnter path to data file (Excel/CSV): ").strip()
    if not os.path.exists(file_path):
        print("Error: File not found!")
        return

    # Load data
    data = load_data_from_file(file_path)
    if data is None:
        return

    # Display data preview
    print(f"\nData Preview:")
    print(f"Columns: {list(data.columns)}")
    print(f"Sample data:")
    print(data.head())

    # Configure analysis
    config = configure_analysis_parameters()

    # Set output file with required naming format
    default_filename = f"{config['client_name']} - Journal Testing.xlsx"
    output_file = input(f"\nEnter output Excel file path (default: {default_filename}): ").strip()
    if not output_file:
        output_file = default_filename

    # Run analysis
    results = run_basic_analysis(data, config, output_file)

    # Display summary
    if results:
        print("\n" + "="*60)
        print("ANALYSIS SUMMARY")
        print("="*60)
        for test_name, result in results.items():
            status = "✓ Completed" if not isinstance(result, str) or not result.startswith("Error") else "✗ Failed"
            print(f"{test_name}: {status}")

    print("\nAnalysis completed. Check the Excel file for detailed results.")


if __name__ == "__main__":
    main()
