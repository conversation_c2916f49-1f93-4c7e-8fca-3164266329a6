#!/usr/bin/env python
"""
Test script for the optimized financial journal testing code.
This script tests the main functionality without requiring user input.
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

# Import the main functions from code.py
try:
    from code import (
        initialize_analysis_environment,
        optimize_dataframe_memory,
        create_analysis_summary,
        round_entries,
        holidaysandweekend
    )
    print("✓ Successfully imported functions from code.py")
except ImportError as e:
    print(f"✗ Error importing from code.py: {e}")
    exit(1)

def create_sample_data():
    """Create sample financial data for testing."""
    print("Creating sample financial data...")
    
    # Generate sample data
    np.random.seed(42)
    n_records = 1000
    
    # Create date range
    start_date = datetime(2023, 1, 1)
    end_date = datetime(2023, 12, 31)
    date_range = pd.date_range(start_date, end_date, freq='D')
    
    data = {
        'Account_Code': np.random.choice(['1000', '2000', '3000', '4000', '5000'], n_records),
        'Document_No': range(1, n_records + 1),
        'Date': np.random.choice(date_range, n_records),
        'Amount': np.random.uniform(-10000, 10000, n_records),
        'Description': [f'Transaction {i}' for i in range(n_records)],
        'Time': [datetime.now().time() for _ in range(n_records)]
    }
    
    # Add some round numbers for testing
    round_indices = np.random.choice(n_records, 50, replace=False)
    for i in round_indices[:25]:
        data['Amount'][i] = np.random.choice([1000, 2000, 5000, 10000])  # 000 endings
    for i in round_indices[25:]:
        data['Amount'][i] = np.random.choice([999, 1999, 4999, 9999])   # 999 endings
    
    df = pd.DataFrame(data)
    
    # Add some reversal entries
    reversal_indices = np.random.choice(n_records, 20, replace=False)
    for i in reversal_indices:
        df.loc[i, 'Description'] = f'Reversal entry {i}'
    
    print(f"✓ Created sample data with {len(df)} records")
    return df

def test_memory_optimization():
    """Test memory optimization function."""
    print("\nTesting memory optimization...")
    
    # Create test dataframe
    df = pd.DataFrame({
        'int_col': [1, 2, 3, 4, 5],
        'float_col': [1.1, 2.2, 3.3, 4.4, 5.5],
        'str_col': ['A', 'B', 'A', 'B', 'A']
    })
    
    original_memory = df.memory_usage(deep=True).sum()
    optimized_df = optimize_dataframe_memory(df.copy())
    optimized_memory = optimized_df.memory_usage(deep=True).sum()
    
    print(f"✓ Memory optimization test passed")
    print(f"  Original memory: {original_memory} bytes")
    print(f"  Optimized memory: {optimized_memory} bytes")
    print(f"  Memory saved: {original_memory - optimized_memory} bytes")

def test_analysis_summary():
    """Test analysis summary function."""
    print("\nTesting analysis summary...")
    
    summary = create_analysis_summary(
        test_name="Test Analysis",
        total_records=1000,
        flagged_records=50,
        conclusion="Test completed successfully"
    )
    
    expected_keys = ['test_name', 'total_records', 'flagged_records', 'flag_percentage', 'conclusion', 'timestamp']
    
    if all(key in summary for key in expected_keys):
        print("✓ Analysis summary test passed")
        print(f"  Flag percentage: {summary['flag_percentage']:.2f}%")
    else:
        print("✗ Analysis summary test failed")

def test_basic_functionality():
    """Test basic functionality without Excel operations."""
    print("\nTesting basic functionality...")
    
    # Initialize environment
    try:
        initialize_analysis_environment()
        print("✓ Environment initialization test passed")
    except Exception as e:
        print(f"✗ Environment initialization failed: {e}")
        return False
    
    # Test memory optimization
    test_memory_optimization()
    
    # Test analysis summary
    test_analysis_summary()
    
    return True

def main():
    """Main test function."""
    print("="*60)
    print("FINANCIAL JOURNAL TESTING - CODE VALIDATION")
    print("="*60)
    
    # Test basic functionality
    if not test_basic_functionality():
        print("\n✗ Basic functionality tests failed")
        return
    
    # Create sample data
    sample_data = create_sample_data()
    
    # Save sample data for manual testing
    sample_file = "sample_financial_data.xlsx"
    try:
        sample_data.to_excel(sample_file, index=False)
        print(f"✓ Sample data saved to: {sample_file}")
    except Exception as e:
        print(f"✗ Error saving sample data: {e}")
    
    print("\n" + "="*60)
    print("CODE VALIDATION COMPLETED")
    print("="*60)
    print("✓ All basic tests passed!")
    print(f"✓ Sample data created: {sample_file}")
    print("\nTo run the full analysis:")
    print("1. Run: python code.py")
    print(f"2. Use the sample file: {sample_file}")
    print("3. Follow the interactive prompts")

if __name__ == "__main__":
    main()
