#!/usr/bin/env python
# coding: utf-8

"""
Financial Journal Testing Script
===============================
This script performs comprehensive analysis on financial journal entries including:
- Round number detection (000, 999 endings)
- Holiday and weekend posting analysis
- Odd hours transaction detection
- Monthly transaction summaries
- Reversal entry identification
- Gap analysis in journal numbers
- Revenue code debit analysis
- Account matching tests

Optimized for terminal-only execution without GUI dependencies.
"""

import warnings
warnings.filterwarnings('ignore')

# Core data processing libraries
import pandas as pd
import xlwings as xw
import numpy as np
import re
import os
import datetime
import calendar

# Configure pandas display format for better readability
pd.options.display.float_format = '{:,.2f}'.format



# Global variables for configuration and data storage
# These variables store column names, account codes, and analysis parameters
wb = None  # Excel workbook object
sheet_name = ""  # Current sheet name
letter = []  # Excel column letters for formatting
n = 0  # Counter variable
num = 1  # Tab counter for Excel sheets

# Account classification lists
rev_code = []  # Revenue account codes
bank_acc = []  # Bank account codes
pre_acc = []  # Prepayment account codes
accrual_acc = []  # Accrual account codes
pl_acc = []  # P&L account codes

# Column name mappings from dataset
account_codes = ""  # Account codes column name
doc_no = ""  # Document number column name
date = ""  # Date column name
amount = ""  # Amount column name
acc_description = ""  # Account description column name
acc_type = ""  # Account type column name
time = ""  # Time column name
post_by = ""  # Posted by column name
date_format = ""  # Date format string
time_format = ""  # Time format string

# Analysis parameters
client_name = ""  # Client name for reports
client_period = ""  # Analysis period
holiday_dates = []  # List of holiday dates
link = []  # Links to Excel tabs

# Data storage
df = None  # Working dataframe
dataset = None  # Main dataset


def summary_sheet():
    """
    Creates a summary sheet in Excel workbook with test descriptions and potential exceptions.
    This function sets up the main overview of all tests to be performed.
    """
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset
    # Get active sheet and set it up as summary
    summary_sheet = wb.sheets.active
    sheet_name = "Summary"
    summary_sheet.name = sheet_name

    # Apply borders to the test summary table
    summary_sheet.range("A5:B21").api.Borders(3).LineStyle = 1
    summary_sheet.range("A5:B20").api.Borders(2).LineStyle = 1

    # Set header information
    summary_sheet["A1"].value = client_name
    summary_sheet.range('A1').api.Font.Bold = True

    summary_sheet["A2"].value = client_period
    summary_sheet.range('A2').api.Font.Bold = True
    summary_sheet["A3"].value = "Subject Journal Testing"
    summary_sheet.range('A3').api.Font.Bold = True

    # Create table headers with formatting
    summary_sheet["A5"].value = "Test"
    summary_sheet.range('A5').api.Font.Bold = True
    summary_sheet['A5'].color = 255, 200, 255
    summary_sheet['A5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    summary_sheet["B5"].value = "Potential exceptions"
    summary_sheet.range('B5').api.Font.Bold = True
    summary_sheet['B5'].color = 255, 200, 255
    summary_sheet['B5'].api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

    # List all available tests
    test_descriptions = [
        "Round entries ,000 or ,999",
        "Date of postings: weekends, bank holidays etc.",
        "Timings of postings - any postings on odd hours",
        "Total amount of transactions per month",
        "Reversed Journal Entries",
        "Gaps/jumps in Journal Entry numbers",
        "Summary of Debit transactions in Revenue codes",
        "Prepayments vs Bank",
        "Accruals vs Bank",
        "Bank accounts vs PnL accounts.",
        "Postings by directors on Companies house",
        "Possible duplicate Journal entries",
        "Fraud Word Check",
        "Sales Chronological Testing",
        "Credits in Revenue"
    ]

    # Populate test descriptions
    for i, description in enumerate(test_descriptions, start=6):
        summary_sheet[f"A{i}"].value = description

    # Add notes about limitations
    notes = [
        "Note 1: No any references were provided regarding details of Time posted",
        f"Note 2: Impossible to perform Gap test as {doc_no} contains characters like text, slashes and hyphens.",
        "Note 3: No any references were provided regarding details of users ID and employee key account."
    ]

    for i, note in enumerate(notes, start=23):
        summary_sheet[f"A{i}"].value = note
        summary_sheet.range(f'A{i}').api.Font.Bold = True

    # Set font for entire summary
    summary_sheet.range("A1:A25").font.name = 'Times New Roman'

    # Auto-fit columns
    wb.sheets[summary_sheet].autofit('c')

    print("Summary sheet created successfully")


def _setup_worksheet_header(worksheet, title):
    """
    Helper function to set up standard worksheet headers.

    Args:
        worksheet: Excel worksheet object
        title: Title for the worksheet
    """
    worksheet["A1"].value = client_name
    worksheet.range('A1').api.Font.Bold = True
    worksheet["A2"].value = client_period
    worksheet.range('A2').api.Font.Bold = True
    worksheet["A3"].value = title
    worksheet.range('A3').api.Font.Bold = True


def _apply_data_formatting(worksheet, data_shape, header_shape, data, include_borders=True):
    """
    Helper function to apply consistent formatting to data ranges.

    Args:
        worksheet: Excel worksheet object
        data_shape: Range string for data area
        header_shape: Range string for header area
        data: DataFrame to format
        include_borders: Whether to include borders
    """
    # Convert datetime objects to strings to avoid Excel issues
    if not data.empty:
        data = data.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)

    # Apply formatting
    if include_borders:
        worksheet.range(data_shape).api.Borders(3).LineStyle = 1
        worksheet.range(data_shape).api.Borders(2).LineStyle = 1
        worksheet.range(header_shape).api.Borders(3).LineStyle = 1
        worksheet.range(header_shape).api.Borders(2).LineStyle = 1

    # Set font and auto-fit
    worksheet.range(header_shape).font.bold = True
    worksheet.range(header_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
    worksheet.range(data_shape).font.name = 'Times New Roman'
    worksheet.range(data_shape).columns.autofit()

    return data
    


# ============================================================================
# TEST 1: ROUND ENTRIES ANALYSIS
# ============================================================================

def round_entries(Amount):
    """
    Analyzes journal entries for round numbers ending in 000 or 999.
    These patterns may indicate potential manipulation or unusual transactions.

    Args:
        Amount: Column name containing transaction amounts

    Returns:
        DataFrame containing entries with round numbers or error message
    """
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset

    link.append("-")  # Initialize link placeholder

    try:
        if amount != "na":
            print(f"Analyzing round entries in column: {amount}")

            # Filter out null values and clean amount data
            entries = dataset[dataset[amount].notnull()].copy()
            entries[amount] = entries[amount].astype(str).str.strip().str.replace(",", "")
            entries = entries[entries[amount] != ""]

            # Find entries ending in 000 (using optimized regex)
            entries_000 = entries[
                entries[amount].astype(str).astype(float).astype(str).str.contains(r"0{3}\.0*$", na=False)
            ].copy()
            if not entries_000.empty:
                entries_000[amount] = pd.to_numeric(entries_000[amount], errors='coerce')
                entries_000 = entries_000.dropna(subset=[amount]).sort_values(amount, ascending=False)

            # Find entries ending in 999 (using optimized regex)
            entries_999 = entries[
                entries[amount].astype(str).astype(float).astype(str).str.contains(r"9{3}\.0*$", na=False)
            ].copy()
            if not entries_999.empty:
                entries_999[amount] = pd.to_numeric(entries_999[amount], errors='coerce')
                entries_999 = entries_999.dropna(subset=[amount]).sort_values(amount, ascending=False)

            # Combine results
            round_entries = pd.concat([entries_000, entries_999], ignore_index=True)

            print(f"Found {len(entries_000)} entries ending in 000")
            print(f"Found {len(entries_999)} entries ending in 999")

            # Create Excel worksheet for round entries analysis
            round_entries_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            link[0] = f"Tab {num}"
            num += 1

            # Set up worksheet headers and formatting
            _setup_worksheet_header(round_entries_tab, "Round entries ,000 or ,999")

            # Add analysis description
            round_entries_tab["A5"].value = "Objective: To find out unusual round number entries in journals."
            round_entries_tab.range('A5').api.Font.Bold = True

            round_entries_tab["A7"].value = 'Method: Filtered all the entries ending with "000" and "999".'
            round_entries_tab.range('A7').api.Font.Bold = True

            # Set up section headers with color coding
            round_entries_tab['A9'].color = 255, 200, 255
            round_entries_tab["B9"].value = "Amount ending in '000'"
            round_entries_tab['A10'].color = 221, 235, 247
            round_entries_tab["B10"].value = "Amount ending in '999'"

            # Calculate dimensions for Excel output
            if not entries_000.empty:
                r = entries_000.shape[0] + 12
                c = entries_000.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                # Apply color coding and formatting for 000 entries
                round_entries_tab.range(data_shape).color = 255, 200, 255
                entries_000 = _apply_data_formatting(round_entries_tab, data_shape, c_shape, entries_000)
                round_entries_tab["A12"].options(pd.DataFrame, index=False).value = entries_000

            # Handle 999 entries section
            if not entries_999.empty:
                r = (entries_000.shape[0] if not entries_000.empty else 0) + 13
                c = entries_999.shape[1]

                data_shape = f"A{r}:{letter[c]}{r + entries_999.shape[0]}"

                # Apply color coding and formatting for 999 entries
                round_entries_tab.range(data_shape).color = 221, 235, 247
                entries_999 = _apply_data_formatting(round_entries_tab, data_shape, "", entries_999, include_borders=True)
                round_entries_tab[f"A{r}"].options(pd.DataFrame, index=False, header=None).value = entries_999

            # Set overall formatting
            total_rows = (entries_000.shape[0] if not entries_000.empty else 0) + (entries_999.shape[0] if not entries_999.empty else 0) + 12
            max_cols = max(entries_000.shape[1] if not entries_000.empty else 0,
                          entries_999.shape[1] if not entries_999.empty else 0)

            if max_cols > 0:
                round_entries_tab.range(f"A1:{letter[max_cols]}{total_rows}").font.name = 'Times New Roman'
                round_entries_tab.range(f"A12:{letter[max_cols]}{total_rows}").columns.autofit()

            print(f"Round entries analysis completed. Total entries found: {len(round_entries)}")

            # Add conclusion based on findings
            cell_no = (entries_000.shape[0] if not entries_000.empty else 0) + \
                     (entries_999.shape[0] if not entries_999.empty else 0) + 16

            # Determine conclusion message
            has_000 = not entries_000.empty and len(entries_000) > 0
            has_999 = not entries_999.empty and len(entries_999) > 0

            if has_000 and has_999:
                conclusion = "Conclusion: Entries ending with '000' & '999' found."
            elif has_000:
                conclusion = "Conclusion: Entries ending with '000' found."
            elif has_999:
                conclusion = "Conclusion: Entries ending with '999' found."
            else:
                conclusion = "Conclusion: No Entries ending with '000' & '999' found."

            round_entries_tab[f"A{cell_no}"].value = conclusion
            round_entries_tab.range(f'A{cell_no}').api.Font.Bold = True
            round_entries_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

            print(conclusion)

        else:
            round_entries = 'Col Name Not Given'
            print("Error: Amount column name not provided")

        return round_entries

    except Exception as e:
        error_msg = f"Error in round_entries analysis: {str(e)}"
        print(error_msg)
        return error_msg


# ============================================================================
# TEST 2: HOLIDAYS AND WEEKEND POSTING ANALYSIS
# ============================================================================

def holidaysandweekend(Date, f):
    """
    Analyzes journal entries posted on weekends and holidays.
    Unusual posting patterns on non-business days may indicate irregularities.

    Args:
        Date: Column name containing transaction dates
        f: Date format string for parsing

    Returns:
        DataFrame containing weekend/holiday transactions or error message
    """
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset

    link.append("-")  # Initialize link placeholder

    try:
        if date != "na" and f != "na":
            print(f"Analyzing holiday and weekend postings in column: {date}")

            date = date.strip()
            df = dataset.copy()

            # Clean and prepare date data
            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]

            # Convert to datetime with error handling
            try:
                df[date] = pd.to_datetime(df[date], format=f, errors='coerce')
                df = df.dropna(subset=[date])  # Remove invalid dates
            except Exception as e:
                print(f"Date parsing error: {e}")
                return "Date parsing failed"

            # Identify holiday transactions
            holidays = holiday_dates
            holiday = df[df[date].isin(holidays)].copy()

            # Identify weekend transactions (optimized using vectorized operations)
            df['day_of_week'] = df[date].dt.day_name()
            weekend_1 = df[df['day_of_week'] == "Saturday"].copy()
            weekend_2 = df[df['day_of_week'] == "Sunday"].copy()

            # Add classification labels
            if not holiday.empty:
                holiday["Holiday"] = "Holiday"
                holiday = holiday.sort_values(amount, ascending=False)

            if not weekend_1.empty:
                weekend_1["Holiday"] = "Saturday"
                weekend_1 = weekend_1.sort_values(amount, ascending=False)

            if not weekend_2.empty:
                weekend_2["Holiday"] = "Sunday"
                weekend_2 = weekend_2.sort_values(amount, ascending=False)

            # Combine weekend data
            weekend = pd.concat([weekend_1, weekend_2], ignore_index=True)

            # Combine all non-business day transactions and remove duplicates
            holidays_trans = pd.concat([holiday, weekend_1, weekend_2], ignore_index=True)
            holidays_trans = holidays_trans.drop_duplicates()

            # Re-separate for display purposes
            holiday = holidays_trans[holidays_trans["Holiday"] == "Holiday"] if "Holiday" in holidays_trans.columns else pd.DataFrame()
            weekend_1 = holidays_trans[holidays_trans["Holiday"] == "Saturday"] if "Holiday" in holidays_trans.columns else pd.DataFrame()
            weekend_2 = holidays_trans[holidays_trans["Holiday"] == "Sunday"] if "Holiday" in holidays_trans.columns else pd.DataFrame()
            weekend = pd.concat([weekend_1, weekend_2], ignore_index=True)

            print(f"Found {len(weekend)} weekend transactions")
            print(f"Found {len(holiday)} holiday transactions")


            # Create Excel worksheet for holiday/weekend analysis
            holidays_trans_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            link[1] = f"Tab {num}"
            num += 1

            # Set up worksheet headers
            _setup_worksheet_header(holidays_trans_tab, "Date of postings: weekends, bank holidays etc.")

            # Add analysis description
            holidays_trans_tab["A5"].value = "Objective: To find out unusual journals entered on holidays and weekends."
            holidays_trans_tab['A5'].font.bold = True

            holidays_trans_tab["A7"].value = 'Method: Filtered all the entries posted on holidays and on weekends.'
            holidays_trans_tab['A7'].font.bold = True

            # Set up section headers with color coding
            holidays_trans_tab['A9'].color = 255, 200, 255
            holidays_trans_tab["B9"].value = 'Posting on "Weekend"'
            holidays_trans_tab['A10'].color = 221, 235, 247
            holidays_trans_tab["B10"].value = 'Posting on "Holiday"'

            # Handle weekend entries section
            if not weekend.empty:
                r = weekend.shape[0] + 12
                c = weekend.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                # Apply color coding and formatting for weekend entries
                holidays_trans_tab.range(data_shape).color = 255, 200, 255
                weekend = _apply_data_formatting(holidays_trans_tab, data_shape, c_shape, weekend)
                holidays_trans_tab["A12"].options(pd.DataFrame, index=False).value = weekend

            # Handle holiday entries section
            if not holiday.empty:
                r = (weekend.shape[0] if not weekend.empty else 0) + 13
                c = holiday.shape[1]

                data_shape = f"A{r}:{letter[c]}{r + holiday.shape[0]}"

                # Apply color coding and formatting for holiday entries
                holidays_trans_tab.range(data_shape).color = 221, 235, 247
                holiday = _apply_data_formatting(holidays_trans_tab, data_shape, "", holiday, include_borders=True)
                holidays_trans_tab[f"A{r}"].options(pd.DataFrame, index=False, header=None).value = holiday

            # Set overall formatting
            total_rows = (weekend.shape[0] if not weekend.empty else 0) + (holiday.shape[0] if not holiday.empty else 0) + 12
            max_cols = max(weekend.shape[1] if not weekend.empty else 0,
                          holiday.shape[1] if not holiday.empty else 0)

            if max_cols > 0:
                holidays_trans_tab.range(f"A1:{letter[max_cols]}{total_rows}").font.name = 'Times New Roman'
                holidays_trans_tab.range(f"A12:{letter[max_cols]}{total_rows}").columns.autofit()

            # Add conclusion based on findings
            cell_no = (weekend.shape[0] if not weekend.empty else 0) + \
                     (holiday.shape[0] if not holiday.empty else 0) + 16

            # Determine conclusion message
            has_weekend = not weekend.empty and len(weekend) > 0
            has_holiday = not holiday.empty and len(holiday) > 0

            if has_weekend and has_holiday:
                conclusion = "Conclusion: Entries posted in 'Weekend' & 'Holiday' found."
            elif has_weekend:
                conclusion = "Conclusion: Entries posted in 'Weekend' found."
            elif has_holiday:
                conclusion = "Conclusion: Entries posted in 'Holiday' found."
            else:
                conclusion = "Conclusion: No Entries posted in 'Weekend' & 'Holiday' found."

            holidays_trans_tab[f"A{cell_no}"].value = conclusion
            holidays_trans_tab.range(f'A{cell_no}').api.Font.Bold = True
            holidays_trans_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

            print(conclusion)
            
        else:
            holidays_trans = 'Col Name Not Given'
            print("Error: Date column name or format not provided")

        return holidays_trans

    except Exception as e:
        error_msg = f"Error in holiday/weekend analysis: {str(e)}"
        print(error_msg)
        return error_msg


# ============================================================================
# PERFORMANCE OPTIMIZATION UTILITIES
# ============================================================================

def optimize_dataframe_memory(df):
    """
    Optimize DataFrame memory usage by downcasting numeric types.

    Args:
        df: DataFrame to optimize

    Returns:
        Optimized DataFrame
    """
    if df.empty:
        return df

    # Optimize numeric columns
    for col in df.select_dtypes(include=['int64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='integer')

    for col in df.select_dtypes(include=['float64']).columns:
        df[col] = pd.to_numeric(df[col], downcast='float')

    # Optimize object columns that might be categorical
    for col in df.select_dtypes(include=['object']).columns:
        if df[col].nunique() / len(df) < 0.5:  # If less than 50% unique values
            df[col] = df[col].astype('category')

    return df


def batch_process_data(data, chunk_size=10000):
    """
    Process large datasets in chunks to improve memory efficiency.

    Args:
        data: DataFrame to process
        chunk_size: Size of each chunk

    Yields:
        DataFrame chunks
    """
    for i in range(0, len(data), chunk_size):
        yield data.iloc[i:i + chunk_size]


def create_analysis_summary(test_name, total_records, flagged_records, conclusion):
    """
    Create a standardized summary for analysis results.

    Args:
        test_name: Name of the test performed
        total_records: Total number of records analyzed
        flagged_records: Number of records flagged
        conclusion: Analysis conclusion

    Returns:
        Dictionary with summary information
    """
    return {
        'test_name': test_name,
        'total_records': total_records,
        'flagged_records': flagged_records,
        'flag_percentage': (flagged_records / total_records * 100) if total_records > 0 else 0,
        'conclusion': conclusion,
        'timestamp': datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    }
    


# ============================================================================
# TEST 3: ODD HOURS POSTING ANALYSIS
# ============================================================================

def odd_hours_entries(Time, f):
    """
    Analyzes journal entries posted during odd hours (outside normal business hours).
    Identifies transactions posted between 8 PM - 8 AM which may indicate irregularities.

    Args:
        Time: Column name containing transaction times
        f: Time format string for parsing

    Returns:
        DataFrame containing odd hours transactions or error message
    """
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount, acc_description
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset

    link.append("-")  # Initialize link placeholder

    try:
        if time != "na" and f != "na":
            print(f"Analyzing odd hours postings in column: {time}")

            time = time.strip()
            df = dataset.copy()

            # Clean and prepare time data
            df[time] = df[time].astype(str).str.strip()
            df = df[df[time].notnull()]
            df = df[df[time] != ""]

            # Convert to datetime with error handling
            try:
                df[time] = pd.to_datetime(df[time], format=f, errors='coerce')
                df = df.dropna(subset=[time])  # Remove invalid times
            except Exception as e:
                print(f"Time parsing error: {e}")
                return "Time parsing failed"

            # Define odd hours (8 PM to 8 AM) - optimized approach
            df['hour'] = df[time].dt.hour

            # Odd hours: 20-23 (8 PM - 11 PM) and 0-8 (12 AM - 8 AM)
            odd_hours_mask = (df['hour'] >= 20) | (df['hour'] <= 8)
            odd_hours_df = df[odd_hours_mask].copy()

            if not odd_hours_df.empty:
                # Add hour labels for better readability
                hour_labels = {
                    0: "12 AM", 1: "1 AM", 2: "2 AM", 3: "3 AM", 4: "4 AM",
                    5: "5 AM", 6: "6 AM", 7: "7 AM", 8: "8 AM",
                    20: "8 PM", 21: "9 PM", 22: "10 PM", 23: "11 PM"
                }
                odd_hours_df["Hours"] = odd_hours_df['hour'].map(hour_labels)

                # Separate AM and PM for display
                odd_hours_am = odd_hours_df[odd_hours_df['hour'] <= 8].copy()
                odd_hours_pm = odd_hours_df[odd_hours_df['hour'] >= 20].copy()

                # Sort by amount for priority analysis
                if not odd_hours_am.empty:
                    odd_hours_am = odd_hours_am.sort_values(amount, ascending=False)
                if not odd_hours_pm.empty:
                    odd_hours_pm = odd_hours_pm.sort_values(amount, ascending=False)

                odd_hours_concat = odd_hours_df.sort_values(amount, ascending=False)

                print(f"Found {len(odd_hours_am)} AM odd hour transactions")
                print(f"Found {len(odd_hours_pm)} PM odd hour transactions")
            else:
                odd_hours_am = pd.DataFrame()
                odd_hours_pm = pd.DataFrame()
                odd_hours_concat = pd.DataFrame()
            
            
            if odd_hours_concat.shape[0] <= 1000000:
            
                odd_hours_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
                link[2] = f"Tab {num}"
                num += 1
                odd_hours_tab["A1"].value = client_name
                odd_hours_tab['A1'].font.bold = True
                odd_hours_tab["A2"].value = client_period
                odd_hours_tab['A2'].font.bold = True
                odd_hours_tab["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab['A3'].font.bold = True


                odd_hours_tab["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab['A5'].font.bold = True

                odd_hours_tab["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab['A7'].font.bold = True


                odd_hours_tab['A9'].color = 255 , 200 , 255
                odd_hours_tab["B9"].value = 'Posting on "AM"'
                odd_hours_tab['A10'].color = 221 , 235 , 247
                odd_hours_tab["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab.range(data_shape).color = 255 , 200 , 255
                import datetime
                odd_hours_am = odd_hours_am.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab["A12"].options(pd.DataFrame, index=False).value = odd_hours_am
                odd_hours_tab.range(c_shape).font.bold = True
                odd_hours_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(data_shape).api.Borders(2).LineStyle =  1
                odd_hours_tab.range(data_shape).api.Borders(4).LineStyle =  1
                odd_hours_tab.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(c_shape).api.Borders(2).LineStyle =  1 

                r = odd_hours_am.shape[0] + 13
                c = odd_hours_pm.shape[1]

                data_shape = f"A{r}:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 13}"
                c_shape = f"A{r}:{letter[c]}{c}"

                odd_hours_tab.range(data_shape).color = 221 , 235 , 247
                import datetime
                odd_hours_pm = odd_hours_pm.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab[f"A{r}"].options(pd.DataFrame, index=False, header = None).value = odd_hours_pm
                odd_hours_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab.range(data_shape).api.Borders(2).LineStyle =  1 
                
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()
                
                cell_no = odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 16
            
                if len(odd_hours_am) > 0 or len(odd_hours_pm) > 0:
                    odd_hours_tab[f"A{cell_no}"].value = "Conclusion: Entries posted in 'Odd hours' found."
                    odd_hours_tab.range(f'A{cell_no}').api.Font.Bold = True
                    odd_hours_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


                else:
                    odd_hours_tab[f"A{cell_no}"].value = "Conclusion: No Entries posted in 'Odd hours' found."
                    odd_hours_tab.range(f'A{cell_no}').api.Font.Bold = True
                    odd_hours_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
                    
                    
            else:
                odd_hours_tab1 = wb.sheets.add("Tab 3.1", after = wb.sheets.active)
                odd_hours_tab1["A1"].value = client_name
                odd_hours_tab1['A1'].font.bold = True
                odd_hours_tab1["A2"].value = client_period
                odd_hours_tab1['A2'].font.bold = True
                odd_hours_tab1["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab1['A3'].font.bold = True


                odd_hours_tab1["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab1['A5'].font.bold = True

                odd_hours_tab1["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab1['A7'].font.bold = True


                odd_hours_tab1['A9'].color = 255 , 200 , 255
                odd_hours_tab1["B9"].value = 'Posting on "AM"'
                odd_hours_tab1['A10'].color = 221 , 235 , 247
                odd_hours_tab1["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab1.range(data_shape).color = 255 , 200 , 255
                import datetime
                odd_hours_am = odd_hours_am.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab1["A12"].options(pd.DataFrame, index=False).value = odd_hours_am
                odd_hours_tab1.range(c_shape).font.bold = True
                odd_hours_tab1.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab1.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab1.range(data_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab1.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab1.range(c_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()

                odd_hours_tab2 = wb.sheets.add("Tab 3.2", after = wb.sheets.active)
                odd_hours_tab2["A1"].value = client_name
                odd_hours_tab2['A1'].font.bold = True
                odd_hours_tab2["A2"].value = client_period
                odd_hours_tab2['A2'].font.bold = True
                odd_hours_tab2["A3"].value = "Timings of postings - any postings on odd hours."
                odd_hours_tab2['A3'].font.bold = True


                odd_hours_tab2["A5"].value = "Objective: To find out unusual journals entered on odd hours."
                odd_hours_tab2['A5'].font.bold = True

                odd_hours_tab2["A7"].value = 'Method: Filtered all the entries posted on odd hours.'
                odd_hours_tab2['A7'].font.bold = True


                odd_hours_tab2['A9'].color = 255 , 200 , 255
                odd_hours_tab2["B9"].value = 'Posting on "AM"'
                odd_hours_tab2['A10'].color = 221 , 235 , 247
                odd_hours_tab2["B10"].value = 'Posting on "PM"'


                r = odd_hours_am.shape[0] + 12
                c = odd_hours_am.shape[1]

                data_shape = f"A13:{letter[c]}{r}"
                c_shape = f"A12:{letter[c]}12"

                odd_hours_tab2.range(data_shape).color = 221 , 235 , 247
                import datetime
                odd_hours_pm = odd_hours_pm.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                odd_hours_tab2["A12"].options(pd.DataFrame, index=False).value = odd_hours_pm
                odd_hours_tab2.range(c_shape).font.bold = True
                odd_hours_tab2.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                odd_hours_tab2.range(data_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab2.range(data_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab2.range(data_shape).api.Borders(4).LineStyle =  1 
                odd_hours_tab2.range(c_shape).api.Borders(3).LineStyle =  1 
                odd_hours_tab2.range(c_shape).api.Borders(2).LineStyle =  1 
                odd_hours_tab.range(f"A1:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").font.name = 'Times New Roman'
                odd_hours_tab.range(f"A12:{letter[c]}{odd_hours_am.shape[0] + odd_hours_pm.shape[0] + 12}").columns.autofit()

        else:
            odd_hours_concat = 'Col Name Not Given'
        return odd_hours_concat
    except Exception as e:
        print(e)
        print("Something else went wrong")
    


# ### TEST 4

# In[6]:


monthly_tab,value = "",""


# In[7]:


def transactions_per_month(Date, f):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    
    global monthly_tab
    global value
    
    link.append("-")
    try:
        if date != "na" and f!="na":
            
            date = date.strip()        
            df = dataset.copy()
            df.sort_values(by = date,inplace = True)
            
            df[date] = df[date].astype(str).str.strip()
            df = df[df[date].notnull()]
            df = df[df[date] != ""]
            df[date] = pd.to_datetime(df[date], format = f)
            df["month"] = df[date].dt.strftime("%m")

            df = df[(df["month"].notnull()) & (df["month"] != "nan")]
            unique_month = df["month"].unique()
            debit_data = df[df[amount] > 0]
            
            #m =  pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].index
            count = pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].values
            sums = pd.pivot_table(debit_data,index = "month",values = amount,aggfunc ="sum",margins = True).iloc[:,0].values
            months =  pd.pivot_table(df,index = "month",values = amount,aggfunc ="count",margins = True).iloc[:,0].index
            
            count_per = count / sum(count[:-1]) 
            sums_per = sums / sum(sums[:-1]) 

            analysis = pd.DataFrame({"Month":months,"No. of Transactions":count,"Value of Transactions":sums,'No. of Trans %':count_per,\
                                    'Value. of Trans %':sums_per})
            #analysis.columns = ["No of Transactions","Value of Transactions"]
            #analysis.insert(0,"Month",analysis.index)

            analysis.sort_values(by = "Month",inplace = True)
            analysis.reset_index(drop = True,inplace = True)

            import calendar
            
            l = []
            temp = list(analysis["Month"].values)
            temp = temp[:len(temp) - 1]
            for i in temp:
                m = calendar.month_abbr[int(i)]
                l.append(m)
            
            l.append("Total")
            analysis["Month"] = l
            
            
#############

            transactions_per_month = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            monthly_tab = f"Tab {num}"
            link[3] = f"Tab {num}"
            num += 1
            transactions_per_month["A1"].value = client_name
            transactions_per_month['A1'].font.bold = True
            transactions_per_month["A2"].value = client_period
            transactions_per_month['A2'].font.bold = True
            transactions_per_month["A3"].value = "Total Amount of € Transactions Per Month."
            transactions_per_month['A3'].font.bold = True
            
            
            transactions_per_month["A5"].value = "Objective: To find out total no. of transactions and value of total transactions per month."
            transactions_per_month['A5'].font.bold = True
            
            transactions_per_month["A7"].value = 'Method: Selected per month Debit entries from Journals and noted count and sum of all transaction for each month.'
            transactions_per_month['A7'].font.bold = True
            
            
            r = analysis.shape[0] + 9
            c = analysis.shape[1]
            
            data_shape = f"A10:{letter[c]}{r}"
            c_shape = f"A9:{letter[c]}9"
            
            import datetime
            analysis = analysis.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            transactions_per_month["A9"].options(pd.DataFrame, index=False).value = analysis
            transactions_per_month.range(c_shape).font.bold = True
            transactions_per_month.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            transactions_per_month.range(c_shape).columns.autofit()
            transactions_per_month.range(f"A10:{letter[c]}{r+1}").api.Borders(3).LineStyle =  1 
            transactions_per_month.range(data_shape).api.Borders(2).LineStyle =  1 
            transactions_per_month.range(c_shape).api.Borders(3).LineStyle =  1 
            transactions_per_month.range(c_shape).api.Borders(2).LineStyle =  1
            transactions_per_month.range(f"A{r}:E{r}").font.bold = True
            transactions_per_month.range(f"A{r}:E{r}").color = 221 , 235 , 247
            
            value = analysis.iloc[:len(analysis)-1]
            
            transactions_per_month.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            transactions_per_month.range(f"A9:{letter[c]}{r}").columns.autofit()
            ####
#             fig = plt.figure(figsize = (6,3.5))
#             ax = fig.add_axes([0,0,1,1])
#             x = "Month"
#             y = "No of Transactions"
#             #plt.xticks(rotation = 45)
#             ax.ticklabel_format(style='plain', axis='y')
#             plt.title(f"{y}",fontsize=20,fontweight = 20)
#             plt.tight_layout()
#             chart1 = ax.bar(data = analysis.iloc[:len(analysis)-1,:],x = x, height = y,color="grey",width = 0.4)
#             plt.xticks(fontsize=13)
#             plt.yticks(fontsize=12)
#             transactions_per_month.pictures.add(fig, name='No Of Transactions', update=True,left = transactions_per_month.range('E9').left,\
#                                                top = transactions_per_month.range('E9').top)

            
#             ####
#             fig = plt.figure(figsize = (6,3.5))
#             ax = fig.add_axes([0,0,1,1])
#             x = "Month"
#             y = "Value of Transactions"
#             #plt.xticks(rotation = 45)
#             ax.ticklabel_format(style='plain', axis='y')
#             plt.title(f"{y}",fontsize=20,fontweight = 20)
#             plt.tight_layout()
#             chart2 = ax.bar(data = analysis.iloc[:len(analysis)-1,:],x = x, height = y,color="grey",width = 0.4)
                
#             plt.xticks(fontsize=13)
#             plt.yticks(fontsize=12)
#             transactions_per_month.pictures.add(fig, name='Value Of Transactions', update=True,left = transactions_per_month.range('N9').left,\
#                                                top = transactions_per_month.range('O9').top)

    
        else:
            analysis = "Col name is not given"
        return analysis
    except Exception as e:
        print(e)
        print("Something else went wrong") 


# ### TEST 5

# In[8]:


def reversed_entries(Acc_description, pttrn = ["reversal","reverse","reversl","reversing"]):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if acc_description != "na":
        acc_description = acc_description.strip()        
        df = dataset.copy()
        df[acc_description] = df[acc_description].astype(str).str.strip()
        import re
        reversal_entries = pd.DataFrame()
        for i in pttrn :
            entries = df[df[acc_description].str.contains(i,flags = re.I) == True]
            reversal_entries = pd.concat([reversal_entries, entries], ignore_index = False)
            reversal_entries = reversal_entries.sort_values(amount, ascending=False)
        
        if len(reversal_entries) > 0:
            reversal_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[4] = f"Tab {num}"
            num += 1
            reversal_tab["A1"].value = client_name
            reversal_tab['A1'].font.bold = True
            reversal_tab["A2"].value = client_period
            reversal_tab['A2'].font.bold = True
            reversal_tab["A3"].value = "Reversed Journal Entries."
            reversal_tab['A3'].font.bold = True


            reversal_tab["A5"].value = "Objective: to identify reversal transaction in journal dump."
            reversal_tab['A5'].font.bold = True

            reversal_tab["A7"].value = 'Method: Selected all reversal transaction posted in the ledger.'
            reversal_tab['A7'].font.bold = True


            r = reversal_entries.shape[0] + 10
            c = reversal_entries.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            reversal_entries = reversal_entries.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            reversal_tab["A10"].options(pd.DataFrame, index=False).value = reversal_entries
            reversal_tab.range(c_shape).font.bold = True
            reversal_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            reversal_tab.range(c_shape).columns.autofit()
            reversal_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            reversal_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            reversal_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            reversal_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            reversal_tab.range(c_shape).api.Borders(2).LineStyle =  1 
            
            reversal_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            reversal_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            
            cell_no = r + 4
            
            if len(reversal_entries) > 0:
                reversal_tab[f"A{cell_no}"].value = "Conclusion: Reversal Transactions found."
                reversal_tab.range(f'A{cell_no}').api.Font.Bold = True
                reversal_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


            else:
                reversal_tab[f"A{cell_no}"].value = "Conclusion: No Reversal Transactions found."
                reversal_tab.range(f'A{cell_no}').api.Font.Bold = True
                reversal_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
        
        else:
            reversal_entries = "Reversal entries not found"

    else:
        reversal_entries = "Col name is not given"
    return reversal_entries


# ### TEST 6

# In[9]:


def gaps(Doc_no):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if doc_no != "na":
        try:
            doc_no = doc_no.strip()        
            df = dataset.copy()
            df = df[df["Journal Entry"].notnull()]
            doc = pd.Series(pd.to_numeric(df["Journal Entry"], downcast='integer').unique())
            gap = []
            doc = doc.sort_values().reset_index(drop = True)
            for i in range(len(doc)):
                if i == 0:
                    gap.append([doc[i],0])
                elif i > 0:
                    v = doc[i] - doc[i-1]
                    gap.append([doc[i],v])

            gaps = pd.DataFrame(gap,columns=["Journal Entry","Gaps in entries"])

            gaps = gaps[gaps["Gaps in entries"]>0]
            
#             doc_no = doc_no.strip()        
#             df = dataset.copy()
#             df = df[df[doc_no].notnull()]
#             doc = pd.Series(df[doc_no].astype(int).unique())
#             gap = []
#             doc = doc.sort_values().reset_index(drop = True)
#             for i in range(len(doc)):
#                 if i == 0:
#                     gap.append(0)
#                 elif i > 0:
#                     v = doc[i] - doc[i-1]
#                     gap.append(v)
#             gap = pd.Series(gap)

#             gaps = pd.DataFrame(columns=[doc_no,"Difference"])
#             gaps[doc_no] = doc
#             gaps["Difference"] = gap

#             gaps = gaps[gaps["Difference"] > 1]
            
            if len(gaps) > 0 :
                gaps_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
                link[5] = f"Tab {num}"
                num += 1
                gaps_tab["A1"].value = client_name
                gaps_tab['A1'].font.bold = True
                gaps_tab["A2"].value = client_period
                gaps_tab['A2'].font.bold = True
                gaps_tab["A3"].value = "Gaps/jumps in Journal Entry numbers."
                gaps_tab['A3'].font.bold = True


                gaps_tab["A5"].value = "Objective: To find out gaps in Journal Entry / Document numbers."
                gaps_tab['A5'].font.bold = True

                gaps_tab["A7"].value = 'Method: All codes and reference test each reference  via formula to locate gaps in document sequence.'
                gaps_tab['A7'].font.bold = True


                r = gaps.shape[0] + 10
                c = gaps.shape[1]

                data_shape = f"A11:{letter[c]}{r}"
                c_shape = f"A10:{letter[c]}10"

                import datetime
                gaps = gaps.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                gaps_tab["A10"].options(pd.DataFrame, index=False).value = gaps
                gaps_tab.range(c_shape).font.bold = True
                gaps_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
                gaps_tab.range(c_shape).columns.autofit()
                gaps_tab.range('A11').columns.autofit()
                gaps_tab.range(data_shape).api.Borders(3).LineStyle =  1 
                gaps_tab.range(data_shape).api.Borders(2).LineStyle =  1 
                gaps_tab.range(data_shape).api.Borders(4).LineStyle =  1 
                gaps_tab.range(c_shape).api.Borders(3).LineStyle =  1 
                gaps_tab.range(c_shape).api.Borders(2).LineStyle =  1 
                
                gaps_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
                gaps_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            else:
                gaps = "Gaps not found"
        except Exception as e:
            print(e)
            gaps = "Something else went wrong"

    else:
        gaps = "Col name is not given"
    
    return gaps


# ### TEST 7

# In[10]:


def rev_debit(Account_codes, Amount):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    if account_codes != "na" and amount != "na":
        
        account_codes = account_codes.strip()
        amount = amount.strip()
        df = dataset.copy()
        df = df[df[account_codes].notnull()]
#         df[account_codes] = df[account_codes].astype(int)
        df[amount] = df[amount].astype("float")
        try:
            df[account_codes] = pd.to_numeric(df[account_codes], downcast='integer')
        except:
            pass
        
        rev_df = df[df[account_codes].astype(str).str.strip().isin(rev_code) & (df[amount] > 0)]
        rev_df = rev_df.sort_values(amount, ascending=False)
        
        if len(rev_df) > 0:
            rev_df_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[6] = f"Tab {num}"
            num += 1
            rev_df_tab["A1"].value = client_name
            rev_df_tab['A1'].font.bold = True
            rev_df_tab["A2"].value = client_period
            rev_df_tab['A2'].font.bold = True
            rev_df_tab["A3"].value = "Summary of Debit transactions in Revenue codes."
            rev_df_tab['A3'].font.bold = True


            rev_df_tab["A5"].value = "Objective: To find out all debit entries in revenue codes."
            rev_df_tab['A5'].font.bold = True

            rev_df_tab["A7"].value = 'Method: Selected all revenue codes and filtered all debits entries.'
            rev_df_tab['A7'].font.bold = True


            r = rev_df.shape[0] + 10
            c = rev_df.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            rev_df = rev_df.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            rev_df_tab["A10"].options(pd.DataFrame, index=False).value = rev_df
            rev_df_tab.range(c_shape).font.bold = True
            rev_df_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            rev_df_tab.range(c_shape).columns.autofit()
            rev_df_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            rev_df_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            rev_df_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            rev_df_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            rev_df_tab.range(c_shape).api.Borders(2).LineStyle =  1 
            
            rev_df_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            rev_df_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            
            
            cell_no = r + 4
            
            if len(rev_df) > 0:
                rev_df_tab[f"A{cell_no}"].value = "Conclusion: Debit Entries found in Revenues Codes"
                rev_df_tab.range(f'A{cell_no}').api.Font.Bold = True
                rev_df_tab.range(f'A{cell_no}').font.name = 'Times New Roman'


            else:
                rev_df_tab[f"A{cell_no}"].value = "Conclusion: No Debit Transactions in Revenue Codes"
                rev_df_tab.range(f'A{cell_no}').api.Font.Bold = True
                rev_df_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
        
        else:
            rev_df = "No Debit Transactions in Revenue Codes"

    else:
        rev_df = "Col Name is not given"
    return rev_df


# ### TEST 8, 9, 10

# In[11]:


def bank_pre(Bank_acc,vs_acc, col_1, col_2):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    try:
        # Step 1: Remove null values for account_codes and doc_no
        entries = dataset[dataset[account_codes].notnull()]
        entries = entries[entries[doc_no].notnull()]
        
        # Step 2: Convert account_codes and doc_no to consistent string format
        try:
            entries[account_codes] = pd.to_numeric(entries[account_codes], downcast="integer").astype(str)
        except:
            entries[account_codes] = entries[account_codes].astype(str)
        
        try:
            entries[doc_no] = pd.to_numeric(entries[doc_no], downcast="integer").astype(str)
        except:
            entries[doc_no] = entries[doc_no].astype(str)
        
        # Step 3: Filter entries for bank and vs accounts
        bank_entries = entries[entries[account_codes].str.strip().isin(bank_acc)]
        vs_entries = entries[entries[account_codes].str.strip().isin(vs_acc)]
        
        # Step 4: Get unique document numbers for both
        bank_doc_no = set(bank_entries[doc_no].unique())
        vs_doc_no = set(vs_entries[doc_no].unique())
        
        # Step 5: Find strictly matched document numbers
        matched_doc_no = bank_doc_no.intersection(vs_doc_no)
        
        # Step 6: Keep only matched records
        bank_acc_df = bank_entries[bank_entries[doc_no].isin(matched_doc_no)].reset_index(drop=True)
        vs_acc_df = vs_entries[vs_entries[doc_no].isin(matched_doc_no)].reset_index(drop=True)
        
        # Step 7: Create DataFrame with matched document numbers
        match_df = pd.DataFrame({col_1: list(matched_doc_no)})
        
        # Step 8: Concatenate only matched records
        concat_df = pd.concat([bank_acc_df, vs_acc_df], ignore_index=True)
        
        # Step 9: Merge to show matched document numbers in a single table
        match = pd.merge(
            pd.DataFrame(bank_acc_df[doc_no].unique(), columns=[col_1]),
            pd.DataFrame(vs_acc_df[doc_no].unique(), columns=[col_2]),
            left_on=col_1, right_on=col_2
        )
        
        # Step 10: Format output
        if bank_acc_df.shape[0] > vs_acc_df.shape[0]:
            df = pd.DataFrame()
            df[col_1] = sorted(bank_acc_df[doc_no].unique().astype(str))
            df[col_2] = sorted(vs_acc_df[doc_no].unique().astype(str))
        else:
            df = pd.DataFrame()
            df[col_2] = sorted(vs_acc_df[doc_no].unique().astype(str))
            df[col_1] = sorted(bank_acc_df[doc_no].unique().astype(str))
        
        # Step 11: Store matched document numbers
        match_acc = list(match[col_1])
        
        # Step 12: Filter only matched transactions
        if len(match_acc) > 0:         
            test = df
            test_record = concat_df[concat_df[doc_no].isin(match_acc)]
            test_record = test_record.sort_values(amount, ascending=False)
        else:
            test = df
            test_record = pd.DataFrame()

#         entries = dataset[dataset[account_codes].notnull()]
#         entries = entries[entries[doc_no].notnull()]
#     #     entries[account_codes] = entries[account_codes].astype(int)

#     #     bank_doc_no = pd.DataFrame(entries[entries[account_codes].astype(str).isin(bank_acc)][document_no].unique())
#     #     vs_doc_no = pd.DataFrame(entries[entries[account_codes].astype(str).isin(vs_acc)][document_no].unique())

# #         try:
# #             bank_doc_no = pd.DataFrame(entries[entries[account_codes].astype("int").astype("str").isin(bank_acc)][doc_no].unique())
# #             vs_doc_no = pd.DataFrame(entries[entries[account_codes].astype("int").astype("str").isin(vs_acc)][doc_no].unique())

# #             bank_acc_df = entries[entries[account_codes].astype("int").astype("str").isin(bank_acc)].reset_index(drop = True)
# #             vs_acc_df = entries[entries[account_codes].astype("int").astype("str").isin(vs_acc)].reset_index(drop = True)
            
# #         except:
# #             bank_doc_no = pd.DataFrame(entries[entries[account_codes].astype("str").isin(bank_acc)][doc_no].unique())
# #             vs_doc_no = pd.DataFrame(entries[entries[account_codes].astype("str").isin(vs_acc)][doc_no].unique())

# #             bank_acc_df = entries[entries[account_codes].astype("str").isin(bank_acc)].reset_index(drop = True)
# #             vs_acc_df = entries[entries[account_codes].astype("str").isin(vs_acc)].reset_index(drop = True)
            
#         try:
#             entries[account_codes] = pd.to_numeric(entries[account_codes], downcast='integer')
#             entries[account_codes] = entries[account_codes].astype("str")
#         except:
#             entries[account_codes] = entries[account_codes].astype("str")
            
#         try:
#             entries[doc_no] = pd.to_numeric(entries[doc_no], downcast='integer')
#             entries[doc_no] = entries[doc_no].astype("str")
#         except:
#             entries[doc_no] = entries[doc_no].astype("str")
            
#         bank_doc_no = pd.DataFrame(entries[entries[account_codes].str.strip().isin(bank_acc)][doc_no].unique())
#         vs_doc_no = pd.DataFrame(entries[entries[account_codes].str.strip().isin(vs_acc)][doc_no].unique())
        
#         bank_acc_df = entries[entries[account_codes].str.strip().isin(bank_acc)].reset_index(drop = True)
#         vs_acc_df = entries[entries[account_codes].str.strip().isin(vs_acc)].reset_index(drop = True)
        
        
#         concat_df = pd.concat([bank_acc_df,vs_acc_df], ignore_index=True)

#         match = pd.merge(bank_doc_no,vs_doc_no, on = 0)

#         if bank_doc_no.shape[0] > vs_doc_no.shape[0]:
#             df = pd.DataFrame()
#             df[col_1] = bank_doc_no[0].astype("str")
#             df[col_2] = vs_doc_no[0].astype("str")
#         else:
#             df = pd.DataFrame()
#             df[col_2] = vs_doc_no[0].astype("str")
#             df[col_1] = bank_doc_no[0].astype("str")

#         match_acc = list(match[0])

#         if len(match_acc) > 0:         
#             test = df
#             test_record = concat_df[concat_df[doc_no].isin(match_acc)]
#         else:
#             test = df
#             test_record = pd.DataFrame()
        


        test_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        if vs_acc == pre_acc:
            link[7] = f"Tab {num}"
        
        elif vs_acc == accrual_acc:
            link[8] = f"Tab {num}"
            
        elif vs_acc == pl_acc:
            link[9] = f"Tab {num}"
        

        n = num
        num += 1
        test_tab["A1"].value = client_name
        test_tab['A1'].font.bold = True
        test_tab["A2"].value = client_period
        test_tab['A2'].font.bold = True
        test_tab["A3"].value = f"{col_2.split()[0]} vs Bank."
        test_tab['A3'].font.bold = True


        test_tab["A5"].value = f"Objective: To find out contra entries of Bank Codes and {col_2.split()[0]} Codes."
        test_tab['A5'].font.bold = True

        test_tab["A7"].value = f'Method: Selected all voucher/journal numbers of Bank Codes and searched for similar voucher/journal numbers in {col_2.split()[0]} ledgers.'
        test_tab['A7'].font.bold = True

        test_tab["A9"].value = f"{col_2.split()[0]} Vs Bank."
        test_tab['A9'].font.bold = True
#         test_tab.range('A9').columns.autofit()

    #     test_tab["A10"].value = 'Accrual Vs Bank'
    #     test_tab['A10'].font.bold = True


        r = test.shape[0] + 12
        c = test.shape[1]

        data_shape = f"A13:{letter[c]}{r}"
        c_shape = f"A12:{letter[c]}12"

        import datetime
        # 255 , 200 , 255
        test = test.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
        test_tab["A12"].options(pd.DataFrame, index=False).value = test
        test_tab.range(c_shape).font.bold = True
        test_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
        test_tab.range(c_shape).columns.autofit()
        test_tab.range('A9').columns.autofit()
        test_tab.range(data_shape).api.Borders(3).LineStyle =  1 
        test_tab.range(data_shape).api.Borders(2).LineStyle =  1
        test_tab.range(data_shape).api.Borders(4).LineStyle =  1
        test_tab.range(c_shape).api.Borders(3).LineStyle =  1 
        test_tab.range(c_shape).api.Borders(2).LineStyle =  1 
        
        test_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        test_tab.range(f"A12:{letter[c]}{r}").columns.autofit()
        test_tab.range('A9').columns.autofit()
        
        
        cell_no = r+4
        if test_record.shape[0] > 0:
            test_tab[f"A{cell_no}"].value = f"Conclusion: Contra entries between Bank Codes and {col_2.split()[0]} Codes found."
            test_tab.range(f'A{cell_no}').api.Font.Bold = True
            test_tab.range(f'A{cell_no}').font.name = 'Times New Roman'

        else:
            test_tab[f"A{cell_no}"].value = f"Conclusion: No Contra entries between Bank codes and {col_2.split()[0]} Codes found."
            test_tab.range(f'A{cell_no}').api.Font.Bold = True
            test_tab.range(f'A{cell_no}').font.name = 'Times New Roman'
            
#         start = timer()
#         for a_cell in test_tab.range(f"A13:A{r}").expand("down"):
#             a = ""
#             if type(a_cell.value) == int or type(a_cell.value) == float:
#                 a = str(int(a_cell.value))

#             if a_cell.value in match_acc or a in match_acc or "0"+a in match_acc or "00"+a in match_acc             or "000"+a in match_acc or "0000"+a in match_acc or "00000"+a in match_acc or "000000"+a in match_acc:
#                 a_cell.color = (255 , 200 , 255)


#         for a_cell in test_tab.range(f"B13:B{r}").expand("down"):
#             a = ""
#             if type(a_cell.value) == int or type(a_cell.value) == float:
#                 a = str(int(a_cell.value))

#             if a_cell.value in match_acc or a in match_acc or "0"+a in match_acc or "00"+a in match_acc             or "000"+a in match_acc or "0000"+a in match_acc or "00000"+a in match_acc or "000000"+a in match_acc:
#                 a_cell.color = (255 , 200 , 255)
#         end = timer()
        
#         print("time taken: ",end - start)

        if test_record.shape[0] > 0:
            r = test_record.shape[0]
            r = r+1
            c = test_record.shape[1]

            data_shape = f"A2:{letter[c]}{r}"
            c_shape = f"A1:{letter[c]}1"
            sht_name = f"Tab{n} Record"


            record_tab = wb.sheets.add(sht_name, after = wb.sheets.active)
            import datetime
            test_record = test_record.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            record_tab["A1"].options(pd.DataFrame, index=False).value = test_record
            record_tab.range(c_shape).font.bold = True
            record_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            record_tab.range(c_shape).columns.autofit()
            record_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            record_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            record_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            record_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            record_tab.range(c_shape).api.Borders(2).LineStyle =  1
            
            record_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            record_tab.range(f"A1:{letter[c]}{r}").columns.autofit()
            
            
            test_tab["B9"].value = f'=HYPERLINK("#\'{sht_name}\'!A1","{sht_name}")'
            return test, test_record
        else:
            return test
    except Exception as e:
        print(e)
        print("Something else went wrong")


# ### TEST 11

# In[12]:


def directors(col):
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global rev_code,bank_acc,pre_acc,accrual_acc,pl_acc 
    global account_codes,doc_no,date,amount,acc_description 
    global acc_type,time,post_by,date_format,time_format 
    global client_name,client_period,holiday_dates,link
    global df, dataset
    link.append("-")
    
    try:
        
        if col != "na":
            col = col.strip()        
            df = dataset.copy()
            df[col] = df[col].astype(str).str.strip()
            director = df[col].value_counts()
            df_director = pd.DataFrame()
            df_director["Posted By"] =  director.index
            df_director["No."] =  director.values

            df_director_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[10] = f"Tab {num}"
            num += 1
            df_director_tab["A1"].value = client_name
            df_director_tab['A1'].font.bold = True
            df_director_tab["A2"].value = client_period
            df_director_tab['A2'].font.bold = True
            df_director_tab["A3"].value = "Postings by directors on Companies house."
            df_director_tab['A3'].font.bold = True

            df_director_tab["A5"].value = "Objective: To Find posting by directors on Company House."
            df_director_tab['A5'].font.bold = True

            df_director_tab["A7"].value = "Method: Count all posting by users on Company House."
            df_director_tab['A7'].font.bold = True

            r = df_director.shape[0] + 10
            c = df_director.shape[1]

            data_shape = f"A11:{letter[c]}{r}"
            c_shape = f"A10:{letter[c]}10"

            import datetime
            df_director = df_director.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
            df_director_tab["A10"].options(pd.DataFrame, index=False).value = df_director
            df_director_tab.range(c_shape).font.bold = True
            df_director_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
            df_director_tab.range(data_shape).api.Borders(3).LineStyle =  1 
            df_director_tab.range(data_shape).api.Borders(2).LineStyle =  1 
            df_director_tab.range(data_shape).api.Borders(4).LineStyle =  1 
            df_director_tab.range(c_shape).api.Borders(3).LineStyle =  1 
            df_director_tab.range(c_shape).api.Borders(2).LineStyle =  1
            
            df_director_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
            df_director_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
            

        else:
            df_director = "Col name is not given"
    
    except Exception as e:
        print(e)
        print("Something else went wrong")  
    
    return df_director


# In[1]:

def dup_entry():
    global wb
    global sheet_name 
    global letter 
    global n
    global num
    global client_name,client_period,link
    global df, dataset
    
    df = dataset.copy()
    
    temp =  df[df.duplicated(keep=False)]
    temp = temp.sort_values(amount, ascending=False)
    
    if len(temp) == 0:
        return 'No dup entry'
    
    else:
        link.append("-")
        df_dup_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[11] = f"Tab {num}"
        num += 1
        df_dup_tab["A1"].value = client_name
        df_dup_tab['A1'].font.bold = True
        df_dup_tab["A2"].value = client_period
        df_dup_tab['A2'].font.bold = True
        df_dup_tab["A3"].value = "Possible duplicate Journal entries."
        df_dup_tab['A3'].font.bold = True

        df_dup_tab["A5"].value = "Objective: To Find possible duplicate Journal entries."
        df_dup_tab['A5'].font.bold = True

        df_dup_tab["A7"].value = "Method: Selected all transactions which are similar to eachother."
        df_dup_tab['A7'].font.bold = True

        r = temp.shape[0] + 10
        c = temp.shape[1]

        data_shape = f"A11:{letter[c]}{r}"
        c_shape = f"A10:{letter[c]}10"

        import datetime
        temp = temp.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
        df_dup_tab["A10"].options(pd.DataFrame, index=False).value = temp
        df_dup_tab.range(c_shape).font.bold = True
        df_dup_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter
        df_dup_tab.range(data_shape).api.Borders(3).LineStyle =  1 
        df_dup_tab.range(data_shape).api.Borders(2).LineStyle =  1 
        df_dup_tab.range(data_shape).api.Borders(4).LineStyle =  1 
        df_dup_tab.range(c_shape).api.Borders(3).LineStyle =  1 
        df_dup_tab.range(c_shape).api.Borders(2).LineStyle =  1

        df_dup_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        df_dup_tab.range(f"A10:{letter[c]}{r}").columns.autofit()
        
        df_dup_tab[f"A{r+4}"].value = f"Conclusion: {len(temp)} duplicate entries found."
        df_dup_tab.range(f'A{r+4}').api.Font.Bold = True
        df_dup_tab.range(f'A{r+4}').font.name = 'Times New Roman'

### #Test 12

def fraud_word_check(acc_description):
    global wb, sheet_name, letter, n, num
    global rev_code, bank_acc, pre_acc, accrual_acc, pl_acc
    global account_codes, doc_no, date, amount
    global acc_type, time, post_by, date_format, time_format
    global client_name, client_period, holiday_dates, link
    global df, dataset
    
    try:
        if acc_description != "na":
            acc_description = acc_description.strip()
            df = dataset.copy()

            # Load fraud keywords
            try:
                key = pd.read_excel("Keywords.xlsx")
                fraud_keywords = key["Words"].dropna().str.strip().tolist()
            except:
                print("Error: Keywords.xlsx file not found")
                return "Keywords file not found"

            # Search for fraud keywords in description
            fraud_entries = pd.DataFrame()
            for keyword in fraud_keywords:
                pattern = rf"\b{keyword}\b"  # match whole words
                temp = df[df[acc_description].str.contains(pattern, na=False, case=False, regex=True)]
                if not temp.empty:
                    temp = temp.copy()
                    temp["Keyword"] = keyword
                    fraud_entries = pd.concat([fraud_entries, temp], ignore_index=True)

            # Create results tab
            # Ensure link list has enough elements
            while len(link) <= 12:
                link.append("-")
            fraud_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
            link[12] = f"Tab {num}"
            num += 1
            # fraud_tab = wb.sheets.add(f"Tab {num}", after=wb.sheets.active)
            # link.append(f"Tab {num}")
            # num += 1

            fraud_tab["A1"].value = client_name
            fraud_tab['A1'].font.bold = True
            fraud_tab["A2"].value = client_period
            fraud_tab['A2'].font.bold = True
            fraud_tab["A3"].value = "Fraud Word Testing"
            fraud_tab['A3'].font.bold = True

            if not fraud_entries.empty:
                # Write fraud matches to Excel
                r = fraud_entries.shape[0] + 7  # headers at row 7
                c = fraud_entries.shape[1]

                data_shape = f"A8:{letter[c]}{r}"
                c_shape = f"A7:{letter[c]}7"

                import datetime
                fraud_entries = fraud_entries.applymap(lambda x: str(x) if isinstance(x, datetime.time) else x)
                fraud_tab["A7"].options(pd.DataFrame, index=False).value = fraud_entries
                fraud_tab.range(c_shape).font.bold = True
                fraud_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

                # Borders
                fraud_tab.range(data_shape).api.Borders(3).LineStyle = 1 
                fraud_tab.range(data_shape).api.Borders(2).LineStyle = 1 
                fraud_tab.range(data_shape).api.Borders(4).LineStyle = 1 
                fraud_tab.range(c_shape).api.Borders(3).LineStyle = 1 
                fraud_tab.range(c_shape).api.Borders(2).LineStyle = 1

                fraud_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
                fraud_tab.range(f"A7:{letter[c]}{r}").columns.autofit()

                fraud_tab[f"A{r+2}"].value = f"Conclusion: {len(fraud_entries)} suspicious entries found."
                fraud_tab[f"A{r+2}"].font.bold = True

            else:
                # No matches found
                fraud_tab["A5"].value = "Objective: To identify potential fraud-related transactions by searching for suspicious keywords."
                fraud_tab["A5"].font.bold = True
                fraud_tab["A7"].value = "Method: Searched all journal descriptions for predefined fraud-related keywords."
                fraud_tab["A7"].font.bold = True
                fraud_tab["A9"].value = "Conclusion: No entries with fraud-related keywords found."
                fraud_tab["A9"].font.bold = True

                fraud_tab.range("A1:A9").font.name = 'Times New Roman'
                fraud_tab.range("A1:A9").columns.autofit()

            return fraud_entries if not fraud_entries.empty else "No fraud entries found"
        
        else:
            return "Column name not provided"

    except Exception as e:
        print(f"Error in fraud_word_check: {e}")
        return "Error processing fraud word check"


#### ##Test 13

def sales_chronological(account_codes, rev_code, doc_no):
    global wb, sheet_name, letter, n, num
    global date
    global client_name, client_period, link
    global df, dataset
    
    try:
        df = dataset.copy()

        # Filter revenue accounts only
        df = df[df[account_codes].astype(str).isin([str(x) for x in rev_code])]
        if df.empty:
            return "No revenue account entries found"

        # Convert Date column
        df[date] = pd.to_datetime(df[date], errors="coerce")

        # Sort by Document Number
        df_sorted = df.sort_values(by="Journal Entry").reset_index(drop=True)

        # Check chronological order for every row
        df_sorted["Chronological Order"] = df_sorted[date].diff().dt.days >= 0
        df_sorted.loc[0, "Chronological Order"] = True  # First row always valid

        # Create results tab
        # Ensure link list has enough elements
        while len(link) <= 13:
            link.append("-")
        chrono_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[13] = f"Tab {num}"
        num += 1
        

        # Header info
        chrono_tab["A1"].value = client_name
        chrono_tab['A1'].font.bold = True
        chrono_tab["A2"].value = client_period
        chrono_tab['A2'].font.bold = True
        chrono_tab["A3"].value = "Chronological Test for Sales Codes"
        chrono_tab['A3'].font.bold = True

        # Objective & Method
        chrono_tab["A5"].value = "Objective: To find out Chronological Order for Sales Code."
        chrono_tab['A5'].font.bold = True
        chrono_tab["A8"].value = 'Note: False In "Chronological Order" shows the Chronological Violation.'
        chrono_tab['A8'].font.bold = True

        # Write full dataset with chronological check
        r = df_sorted.shape[0] + 12
        c = df_sorted.shape[1]
        data_shape = f"A13:{letter[c]}{r}"   # data rows
        c_shape = f"A12:{letter[c]}12"      # header row

        chrono_tab["A12"].options(pd.DataFrame, index=False).value = df_sorted
        chrono_tab.range(c_shape).font.bold = True
        chrono_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

        # Borders
        chrono_tab.range(data_shape).api.Borders(3).LineStyle = 1 
        chrono_tab.range(data_shape).api.Borders(2).LineStyle = 1 
        chrono_tab.range(data_shape).api.Borders(4).LineStyle = 1 
        chrono_tab.range(c_shape).api.Borders(3).LineStyle = 1 
        chrono_tab.range(c_shape).api.Borders(2).LineStyle = 1

        # Formatting
        chrono_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        chrono_tab.range(f"A12:{letter[c]}{r}").columns.autofit()

        # Conclusion
        violations = df_sorted[df_sorted["Chronological Order"] == False]
        if not violations.empty:
            chrono_tab[f"A{r+2}"].value = f"Conclusion: {len(violations)} out-of-order entries found."
        else:
            chrono_tab[f"A{r+2}"].value = "Conclusion: All revenue entries follow chronological order."
        chrono_tab[f"A{r+2}"].font.bold = True

        return df_sorted

    except Exception as e:
        print(f"Error in sales_chronological: {e}")
        return "Error processing sales chronological test"

### ##TEST14

def credits_in_revenue(account_codes, rev_code, doc_no):
    global wb, sheet_name, letter, n, num
    global client_name, client_period, link
    global df, dataset
    
    try:
        df = dataset.copy()

        # Step 1: Identify all Document Numbers linked to revenue accounts
        revenue_docs = df[df[account_codes].astype(str).isin([str(x) for x in rev_code])][doc_no].unique().tolist()
        if not revenue_docs:
            return "No revenue-related documents found"

        # Step 2: Extract all entries with those Document Numbers
        revenue_related = df[df[doc_no].isin(revenue_docs)].reset_index(drop=True)

        # Step 3: Create results tab
        # Ensure link list has enough elements
        while len(link) <= 14:
            link.append("-")
        credits_tab = wb.sheets.add(f"Tab {num}", after = wb.sheets.active)
        link[14] = f"Tab {num}"
        num += 1

        # Header info
        credits_tab["A1"].value = client_name
        credits_tab['A1'].font.bold = True
        credits_tab["A2"].value = client_period
        credits_tab['A2'].font.bold = True
        credits_tab["A3"].value = "Revenue entires with dr posted to GLs."
        credits_tab['A3'].font.bold = True

        # Step 4: Write table at A7
        r = revenue_related.shape[0] + 7
        c = revenue_related.shape[1]
        data_shape = f"A8:{letter[c]}{r}"   # data rows
        c_shape = f"A7:{letter[c]}7"        # header row

        credits_tab["A7"].options(pd.DataFrame, index=False).value = revenue_related
        credits_tab.range(c_shape).font.bold = True
        credits_tab.range(c_shape).api.HorizontalAlignment = xw.constants.HAlign.xlHAlignCenter

        # Borders
        credits_tab.range(data_shape).api.Borders(3).LineStyle = 1 
        credits_tab.range(data_shape).api.Borders(2).LineStyle = 1 
        credits_tab.range(data_shape).api.Borders(4).LineStyle = 1 
        credits_tab.range(c_shape).api.Borders(3).LineStyle = 1 
        credits_tab.range(c_shape).api.Borders(2).LineStyle = 1

        # Formatting
        credits_tab.range(f"A1:{letter[c]}{r}").font.name = 'Times New Roman'
        credits_tab.range(f"A7:{letter[c]}{r}").columns.autofit()

        # Step 5: Conclusion
        credits_tab[f"A{r+2}"].value = f"Conclusion: {len(revenue_related)} entries found linked to revenue documents."
        credits_tab[f"A{r+2}"].font.bold = True

        return revenue_related

    except Exception as e:
        print(f"Error in credits_in_revenue: {e}")
        return "Error processing credits in revenue test"


# ============================================================================
# LEGACY FUNCTION - KEPT FOR COMPATIBILITY
# ============================================================================

def jt_legacy():
    """
    Legacy function for backward compatibility.
    Use main() function for new implementations.
    """
    print("This function has been deprecated. Please use main() function instead.")
    print("Run: python code.py")
    return None


# ============================================================================
# GLOBAL VARIABLE INITIALIZATION
# ============================================================================

# Initialize global variables for backward compatibility
sheet_name = ""
wb = ""
letter = ""
n = 1
rev_code, bank_acc, pre_acc, accrual_acc, pl_acc = "", "", "", "", ""
account_codes, doc_no, date, amount, acc_description = "", "", "", "", ""
acc_type, time, post_by, date_format, time_format = '', "", "", "", ""
client_name, client_period, holiday_dates, link = "", "", "", ""
df, dataset = None, None
monthly_tab, value = "", ""


# ============================================================================
# MAIN EXECUTION FUNCTIONS - TERMINAL INTERFACE
# ============================================================================

def initialize_analysis_environment():
    """
    Initialize the analysis environment with default settings.
    """
    global letter, num, link

    # Initialize Excel column letters for formatting
    letter = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    letter.extend([f"A{chr(i)}" for i in range(ord('A'), ord('Z') + 1)])

    # Initialize counters
    num = 1
    link = []

    print("Analysis environment initialized successfully")
            
#             chart = openpyxl.chart.LineChart(marker = True)
#             chart.height = 7
#             chart.width = 13

#             chart.add_data(values, titles_from_data=True,from_rows=True)
#             chart.add_data(values1, titles_from_data=True,from_rows=True)

#             chart.set_categories(cats)
#             chart.legend.position = 'b'

#             line = chart.series[0]
#             line.marker.symbol = "circle"
#             line = chart.series[1]
#             line.marker.symbol = "circle"

#             chart.title = f"Sales vs COS {year_only}"
#             w.add_chart(chart,"A33")
            
            # Just take the data from last column
            values = Reference(sheet,
                               min_col=2,
                               max_col=2,
                               min_row=9,
                               max_row=9+l)

            cats = Reference(sheet, min_col=1, max_col=1, min_row=10, max_row=10+l)

            # Create object of BarChart class
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            # set the title of the chart
            chart.title = "No. Of Transaction"

            sheet.add_chart(chart,"P9")


            ######################
            values = Reference(sheet,
                               min_col=3,
                               max_col=3,
                               min_row=9,
                               max_row=9+l)

            cats = Reference(sheet, min_col=1, max_col=1, min_row=10, max_row=10+l)

            # Create object of BarChart class
            chart = BarChart()
            chart.height = 9
            chart.width = 14
            chart.add_data(values, titles_from_data=True)
            chart.set_categories(cats)
            chart.legend = None
            # set the title of the chart
            chart.title = "Value Of Transaction"

            sheet.add_chart(chart,"Y9")

            # save the file 
            #wb.save(f"{file_name}.xlsx")

            from openpyxl.styles.borders import Border, Side, BORDER_THIN

            thin_border = Border(
                left=Side(border_style=BORDER_THIN, color='00000000'),
                right=Side(border_style=BORDER_THIN, color='00000000'),
                top=Side(border_style=BORDER_THIN, color='00000000'),
                bottom=Side(border_style=BORDER_THIN, color='00000000')
            )

            #wb = openpyxl.load_workbook(f'{file_name}.xlsx')
            sheet = wb["Summary"]

            n = 5
            for i in link:
                n+=1
                if i != "-":
                    sheet[f"B{n}"] = f'=HYPERLINK("#\'{i}\'!A1","{i}")'
                    sheet[f"B{n}"].style = "Hyperlink"
                    sheet.cell(row=n, column=2).border  = thin_border

            wb.save(f"{file_name}.xlsx")

            import os
            n = client_name.split(":")
            name = n[1].strip()
            # os.rename(f"{file_name}.xlsx",f"{name} - Journal Testing.xlsx")

            print("Analysis completed successfully!")

    except Exception as e:
        for i in os.listdir():
            if i.startswith("Book"):
                os.remove(i)
        print(f"Error occurred: {e}")
        print("Something went wrong during analysis")


# In[14]:


sheet_name = ""
wb = ""
letter = ""
n = 1
rev_code,bank_acc,pre_acc,accrual_acc,pl_acc = "","","","",""
account_codes,doc_no,date,amount,acc_description = "","","","",""
acc_type,time,post_by,date_format,time_format = '',"","","",""
client_name,client_period,holiday_dates,link = "","","",""
df, dataset = "",""


# In[15]:


window = Tk()
window.title('JT')

app_width = 550
app_height = 650

style = Style()
style.configure('W.TButton', font =  ('calibri', 14, 'bold'),foreground = 'Blue',background='Blue')

window.geometry(f'{app_width}x{app_height}+380+20')
window.resizable(False,False)

# title = Label(window,text = "PTIS", font = ("Arial Bold",30)).place(x=300,y=5)
title = Label(window,text = "Journal Testing",font = ("Arial Bold",20),foreground='red').place(x=170,y=10)

test1 = Label(window,text = "Test 1:     Round entries ,000 or ,999",font = ("calibri",12))
test2 = Label(window,text = "Test 2:     Date of postings: weekends, bank holidays etc.",font = ("calibri",12))
test3 = Label(window,text = "Test 3:     Timings of postings - any postings on odd hours.",font = ("calibri",12))
test4 = Label(window,text = "Test 4:     Total amount of transactions per month",font = ("calibri",12))
test5 = Label(window,text = "Test 5:     Reversed Journal Entries",font = ("calibri",12))
test6 = Label(window,text = "Test 6:     Gaps/jumps in Journal Entry numbers",font = ("calibri",12))
test7 = Label(window,text = "Test 7:     Summary of Debit transactions in Revenue codes",font = ("calibri",12))
test8 = Label(window,text = "Test 8:     Prepayments vs Bank",font = ("calibri",12))
test9 = Label(window,text = "Test 9:     Accruals vs Bank",font = ("calibri",12))
test10 = Label(window,text = "Test 10:   Bank accounts vs PnL accounts.",font = ("calibri",12))
test11 = Label(window,text = "Test 11:   Postings by directors on Companies house.",font = ("calibri",12))
test12 = Label(window,text = "Test 12:   Possible duplicate Journal entries.",font = ("calibri",12))
test13 = Label(window,text = "Test 13:   Fraud Word Check.",font = ("calibri",12))
test14 = Label(window,text = "Test 14:   Sales Chronological Testing.",font = ("calibri",12))
test15 = Label(window,text = "Test 15:   Credits in Revenue.",font = ("calibri",12))



label_file = Label(window,text = "File Name: ",font = ("calibri",16),foreground="Red")
entry_file = Entry(window,width = 26,font=('Arial 12'))
bt = Button(window,text = "Test All",style='W.TButton',command = jt)

pos = 90
gap = 30
test1.place(x= 20, y= pos)
pos += gap
test2.place(x= 20, y= pos)
pos += gap
test3.place(x= 20, y= pos)
pos += gap
test4.place(x= 20, y= pos)
pos += gap
test5.place(x= 20, y= pos)
pos += gap
test6.place(x= 20, y= pos)
pos += gap
test7.place(x= 20, y= pos)
pos += gap
test8.place(x= 20, y= pos)
pos += gap
test9.place(x= 20, y= pos)
pos += gap
test10.place(x= 20, y= pos)
pos += gap
test11.place(x= 20, y= pos)
pos += gap
test12.place(x= 20, y= pos)
pos += gap
test13.place(x= 20, y= pos)
pos += gap
test14.place(x= 20, y= pos)
pos += gap
test15.place(x= 20, y= pos)

pos += 60
label_file.place(x=90,y=pos-3)
entry_file.place(x=195,y=pos)

pos += 40
bt.place(x= 215, y= pos)

# wb = xw.Book()
# letter = {1: 'A', 2: 'B', 3: 'C', 4: 'D', 5: 'E', 6: 'F', 7: 'G', 8: 'H', 9: 'I', 10: 'J', 11: 'K', 12: 'L', 13: 'M', 14: 'N', 15: 'O', 16: 'P', 17: 'Q', 18: 'R', 19: 'S', 20: 'T', 21: 'U', 22: 'V', 23: 'W', 24: 'X', 25: 'Y', 26: 'Z', 27: 'AA', 28: 'AB', 29: 'AC', 30: 'AD', 31: 'AE', 32: 'AF', 33: 'AG', 34: 'AH', 35: 'AI', 36: 'AJ', 37: 'AK', 38: 'AL', 39: 'AM', 40: 'AN', 41: 'AO', 42: 'AP', 43: 'AQ', 44: 'AR', 45: 'AS', 46: 'AT', 47: 'AU', 48: 'AV', 49: 'AW', 50: 'AX', 51: 'AY', 52: 'AZ', 53: 'BA', 54: 'BB', 55: 'BC', 56: 'BD', 57: 'BE', 58: 'BF', 59: 'BG', 60: 'BH', 61: 'BI', 62: 'BJ', 63: 'BK', 64: 'BL', 65: 'BM', 66: 'BN', 67: 'BO', 68: 'BP', 69: 'BQ', 70: 'BR', 71: 'BS', 72: 'BT', 73: 'BU', 74: 'BV', 75: 'BW', 76: 'BX', 77: 'BY', 78: 'BZ', 79: 'CA', 80: 'CB', 81: 'CC', 82: 'CD', 83: 'CE', 84: 'CF', 85: 'CG', 86: 'CH', 87: 'CI', 88: 'CJ', 89: 'CK', 90: 'CL', 91: 'CM', 92: 'CN', 93: 'CO', 94: 'CP', 95: 'CQ', 96: 'CR', 97: 'CS', 98: 'CT', 99: 'CU'}

# ============================================================================
# MAIN EXECUTION FUNCTIONS - TERMINAL INTERFACE
# ============================================================================

def initialize_analysis_environment():
    """
    Initialize the analysis environment with default settings.
    """
    global letter, num, link

    # Initialize Excel column letters for formatting
    letter = [chr(i) for i in range(ord('A'), ord('Z') + 1)]
    letter.extend([f"A{chr(i)}" for i in range(ord('A'), ord('Z') + 1)])

    # Initialize counters
    num = 1
    link = []

    print("Analysis environment initialized successfully")


def load_data_from_file(file_path):
    """
    Load data from Excel or CSV file with error handling.

    Args:
        file_path: Path to the data file

    Returns:
        DataFrame or None if loading fails
    """
    try:
        if file_path.endswith('.xlsx') or file_path.endswith('.xls'):
            data = pd.read_excel(file_path)
        elif file_path.endswith('.csv'):
            data = pd.read_csv(file_path)
        else:
            print("Unsupported file format. Please use Excel (.xlsx, .xls) or CSV (.csv)")
            return None

        print(f"Data loaded successfully: {len(data)} rows, {len(data.columns)} columns")
        return optimize_dataframe_memory(data)

    except Exception as e:
        print(f"Error loading data: {str(e)}")
        return None


def configure_analysis_parameters():
    """
    Interactive configuration of analysis parameters via terminal input.

    Returns:
        Dictionary with configuration parameters
    """
    print("\n" + "="*60)
    print("FINANCIAL JOURNAL TESTING - CONFIGURATION")
    print("="*60)

    config = {}

    # Basic information
    config['client_name'] = input("Enter client name: ").strip()
    config['client_period'] = input("Enter analysis period (e.g., 2023 Q1): ").strip()

    # Column mappings
    print("\nColumn Mapping Configuration:")
    config['account_codes'] = input("Enter account codes column name: ").strip()
    config['doc_no'] = input("Enter document number column name: ").strip()
    config['date'] = input("Enter date column name: ").strip()
    config['amount'] = input("Enter amount column name: ").strip()
    config['acc_description'] = input("Enter account description column name: ").strip()
    config['time'] = input("Enter time column name (or 'na' if not available): ").strip()

    # Date/time formats
    config['date_format'] = input("Enter date format (e.g., %Y-%m-%d): ").strip()
    config['time_format'] = input("Enter time format (e.g., %H:%M:%S) or 'na': ").strip()

    # Account classifications
    print("\nAccount Classification (comma-separated codes):")
    config['rev_code'] = [x.strip() for x in input("Enter revenue account codes: ").split(',') if x.strip()]
    config['bank_acc'] = [x.strip() for x in input("Enter bank account codes: ").split(',') if x.strip()]
    config['pre_acc'] = [x.strip() for x in input("Enter prepayment account codes: ").split(',') if x.strip()]
    config['accrual_acc'] = [x.strip() for x in input("Enter accrual account codes: ").split(',') if x.strip()]
    config['pl_acc'] = [x.strip() for x in input("Enter P&L account codes: ").split(',') if x.strip()]

    # Holiday dates
    holiday_input = input("Enter holiday dates (YYYY-MM-DD, comma-separated) or 'na': ").strip()
    if holiday_input.lower() != 'na':
        try:
            config['holiday_dates'] = [pd.to_datetime(x.strip()) for x in holiday_input.split(',') if x.strip()]
        except:
            config['holiday_dates'] = []
            print("Warning: Invalid holiday date format, using empty list")
    else:
        config['holiday_dates'] = []

    return config


def run_comprehensive_analysis(data, config, output_file):
    """
    Run all available tests on the dataset.

    Args:
        data: DataFrame containing journal entries
        config: Configuration dictionary
        output_file: Path for Excel output file

    Returns:
        Dictionary with analysis results
    """
    global wb, dataset, client_name, client_period, holiday_dates
    global account_codes, doc_no, date, amount, acc_description, time
    global date_format, time_format, rev_code, bank_acc, pre_acc, accrual_acc, pl_acc

    # Set global variables
    dataset = data
    client_name = config['client_name']
    client_period = config['client_period']
    holiday_dates = config['holiday_dates']
    account_codes = config['account_codes']
    doc_no = config['doc_no']
    date = config['date']
    amount = config['amount']
    acc_description = config['acc_description']
    time = config['time']
    date_format = config['date_format']
    time_format = config['time_format']
    rev_code = config['rev_code']
    bank_acc = config['bank_acc']
    pre_acc = config['pre_acc']
    accrual_acc = config['accrual_acc']
    pl_acc = config['pl_acc']

    # Initialize Excel workbook with proper naming
    try:
        wb = xw.Book()
        # Ensure the output file has the correct format
        if not output_file.endswith('.xlsx'):
            output_file += '.xlsx'

        # Extract client name for proper formatting if needed
        if " - Journal Testing" not in output_file:
            base_name = output_file.replace('.xlsx', '')
            output_file = f"{client_name} - Journal Testing.xlsx"

        wb.save(output_file)
        print(f"Excel workbook created: {output_file}")
    except Exception as e:
        print(f"Error creating Excel workbook: {e}")
        return None

    results = {}

    print("\n" + "="*60)
    print("RUNNING COMPREHENSIVE ANALYSIS")
    print("="*60)

    # Test 1: Round entries
    print("\n1. Analyzing round entries (000, 999)...")
    try:
        results['round_entries'] = round_entries(amount)
        print("✓ Round entries analysis completed")
    except Exception as e:
        print(f"✗ Round entries analysis failed: {e}")
        results['round_entries'] = f"Error: {e}"

    # Test 2: Holiday and weekend postings
    print("\n2. Analyzing holiday and weekend postings...")
    try:
        results['holiday_weekend'] = holidaysandweekend(date, date_format)
        print("✓ Holiday/weekend analysis completed")
    except Exception as e:
        print(f"✗ Holiday/weekend analysis failed: {e}")
        results['holiday_weekend'] = f"Error: {e}"

    # Test 3: Odd hours postings
    if time != "na" and time_format != "na":
        print("\n3. Analyzing odd hours postings...")
        try:
            results['odd_hours'] = odd_hours_entries(time, time_format)
            print("✓ Odd hours analysis completed")
        except Exception as e:
            print(f"✗ Odd hours analysis failed: {e}")
            results['odd_hours'] = f"Error: {e}"
    else:
        print("\n3. Skipping odd hours analysis (time data not available)")
        results['odd_hours'] = "Skipped - no time data"

    # Test 4: Monthly transactions
    print("\n4. Analyzing monthly transaction patterns...")
    try:
        results['monthly_transactions'] = transactions_per_month(date, date_format)
        print("✓ Monthly transactions analysis completed")
    except Exception as e:
        print(f"✗ Monthly transactions analysis failed: {e}")
        results['monthly_transactions'] = f"Error: {e}"

    # Test 5: Reversal entries
    print("\n5. Analyzing reversal entries...")
    try:
        results['reversal_entries'] = reversed_entries(acc_description)
        print("✓ Reversal entries analysis completed")
    except Exception as e:
        print(f"✗ Reversal entries analysis failed: {e}")
        results['reversal_entries'] = f"Error: {e}"

    # Test 6: Gap analysis
    print("\n6. Analyzing gaps in journal numbers...")
    try:
        results['gaps'] = gaps(doc_no)
        print("✓ Gap analysis completed")
    except Exception as e:
        print(f"✗ Gap analysis failed: {e}")
        results['gaps'] = f"Error: {e}"

    # Test 7: Revenue debit analysis
    if rev_code:
        print("\n7. Analyzing debit entries in revenue codes...")
        try:
            results['revenue_debits'] = rev_debit(account_codes, amount)
            print("✓ Revenue debit analysis completed")
        except Exception as e:
            print(f"✗ Revenue debit analysis failed: {e}")
            results['revenue_debits'] = f"Error: {e}"
    else:
        print("\n7. Skipping revenue debit analysis (no revenue codes provided)")
        results['revenue_debits'] = "Skipped - no revenue codes"

    # Create summary sheet
    print("\n8. Creating summary sheet...")
    try:
        summary_sheet()
        print("✓ Summary sheet created")
    except Exception as e:
        print(f"✗ Summary sheet creation failed: {e}")

    # Save workbook with final naming
    try:
        # Ensure final file name follows the required format
        final_filename = f"{client_name} - Journal Testing.xlsx"
        wb.save(final_filename)

        # If the original output_file was different, rename it
        if output_file != final_filename and os.path.exists(output_file):
            try:
                os.rename(output_file, final_filename)
            except:
                pass  # File might already be correctly named

        print(f"\n✓ Analysis completed successfully!")
        print(f"Results saved to: {final_filename}")

        # Update output_file for return reference
        output_file = final_filename

    except Exception as e:
        print(f"✗ Error saving workbook: {e}")

    return results


def main():
    """
    Main function to run the financial journal testing application.
    """
    print("="*80)
    print("FINANCIAL JOURNAL TESTING SYSTEM")
    print("Optimized Terminal Version")
    print("="*80)

    # Initialize environment
    initialize_analysis_environment()

    # Get data file path
    file_path = input("\nEnter path to data file (Excel/CSV): ").strip()
    if not os.path.exists(file_path):
        print("Error: File not found!")
        return

    # Load data
    data = load_data_from_file(file_path)
    if data is None:
        return

    # Display data preview
    print(f"\nData Preview:")
    print(f"Columns: {list(data.columns)}")
    print(f"Sample data:")
    print(data.head())

    # Configure analysis
    config = configure_analysis_parameters()

    # Set output file with required naming format
    default_filename = f"{config['client_name']} - Journal Testing.xlsx"
    output_file = input(f"\nEnter output Excel file path (default: {default_filename}): ").strip()
    if not output_file:
        output_file = default_filename

    # Run analysis
    results = run_comprehensive_analysis(data, config, output_file)

    # Display summary
    if results:
        print("\n" + "="*60)
        print("ANALYSIS SUMMARY")
        print("="*60)
        for test_name, result in results.items():
            status = "✓ Completed" if not isinstance(result, str) or not result.startswith("Error") else "✗ Failed"
            print(f"{test_name}: {status}")

    print("\nAnalysis completed. Check the Excel file for detailed results.")


if __name__ == "__main__":
    main()